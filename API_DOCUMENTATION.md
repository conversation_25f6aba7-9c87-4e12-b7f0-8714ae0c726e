# 🔌 API Documentation

## Overview

The Attendance System provides a RESTful API for integration with external systems, mobile applications, and third-party services. All API endpoints return JSON responses and support both Arabic and English languages.

**Base URL**: `http://localhost:5000/api/v1`

**Authentication**: <PERSON><PERSON> (JWT) or Session-based

---

## Authentication

### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
```

**Response:**
```json
{
    "success": true,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "username": "admin",
        "role": "admin"
    },
    "expires_in": 3600
}
```

### Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

---

## Employees API

### Get All Employees
```http
GET /api/v1/employees
Authorization: Bearer <token>
```

**Query Parameters:**
- `branch_id` (optional): Filter by branch
- `status` (optional): active, inactive
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "أحمد محمد",
            "job_title": "مطور برمجيات",
            "branch_id": 1,
            "branch_name": "الفرع الرئيسي",
            "phone": "+966501234567",
            "email": "<EMAIL>",
            "status": "active",
            "hire_date": "2024-01-01",
            "image_url": "/static/uploads/employees/emp_1.jpg"
        }
    ],
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 50,
        "pages": 3
    }
}
```

### Get Employee by ID
```http
GET /api/v1/employees/{id}
Authorization: Bearer <token>
```

### Create New Employee
```http
POST /api/v1/employees
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
    "name": "سارة أحمد",
    "job_title": "مديرة الموارد البشرية",
    "branch_id": 1,
    "phone": "+966501234568",
    "email": "<EMAIL>",
    "hire_date": "2024-01-15",
    "image": <file>
}
```

### Update Employee
```http
PUT /api/v1/employees/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "سارة أحمد محدث",
    "job_title": "مديرة الموارد البشرية الأولى",
    "phone": "+966501234569"
}
```

### Delete Employee
```http
DELETE /api/v1/employees/{id}
Authorization: Bearer <token>
```

---

## Attendance API

### Record Attendance (Check In/Out)
```http
POST /api/v1/attendance/record
Authorization: Bearer <token>
Content-Type: application/json

{
    "emp_id": 1,
    "action": "check_in",  // or "check_out"
    "timestamp": "2024-01-15T08:30:00Z",
    "method": "manual",    // or "camera"
    "notes": "تسجيل دخول يدوي"
}
```

**Response:**
```json
{
    "success": true,
    "message": "تم تسجيل الحضور بنجاح",
    "data": {
        "id": 123,
        "emp_id": 1,
        "employee_name": "أحمد محمد",
        "date": "2024-01-15",
        "time_in": "08:30:00",
        "time_out": null,
        "status": "present",
        "method": "manual"
    }
}
```

### Get Attendance Records
```http
GET /api/v1/attendance
Authorization: Bearer <token>
```

**Query Parameters:**
- `emp_id` (optional): Filter by employee
- `branch_id` (optional): Filter by branch
- `date_from` (optional): Start date (YYYY-MM-DD)
- `date_to` (optional): End date (YYYY-MM-DD)
- `status` (optional): present, absent, late
- `page` (optional): Page number
- `per_page` (optional): Items per page

### Get Today's Attendance
```http
GET /api/v1/attendance/today
Authorization: Bearer <token>
```

### Get Employee Attendance History
```http
GET /api/v1/attendance/employee/{emp_id}
Authorization: Bearer <token>
```

---

## Camera Recognition API

### Start Camera Session
```http
POST /api/v1/camera/start
Authorization: Bearer <token>
Content-Type: application/json

{
    "camera_index": 0,
    "recognition_threshold": 0.6
}
```

### Capture and Recognize
```http
POST /api/v1/camera/recognize
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
    "image": <base64_encoded_image>,
    "action": "check_in"  // or "check_out"
}
```

**Response:**
```json
{
    "success": true,
    "recognized": true,
    "employee": {
        "id": 1,
        "name": "أحمد محمد",
        "confidence": 0.85
    },
    "attendance": {
        "id": 124,
        "action": "check_in",
        "timestamp": "2024-01-15T08:30:00Z"
    }
}
```

### Stop Camera Session
```http
POST /api/v1/camera/stop
Authorization: Bearer <token>
```

---

## Reports API

### Generate Attendance Report
```http
POST /api/v1/reports/attendance
Authorization: Bearer <token>
Content-Type: application/json

{
    "report_type": "daily",  // daily, weekly, monthly
    "date_from": "2024-01-01",
    "date_to": "2024-01-31",
    "branch_id": 1,
    "emp_ids": [1, 2, 3],
    "format": "pdf",  // pdf, excel, json
    "language": "ar"  // ar, en
}
```

**Response:**
```json
{
    "success": true,
    "report_id": "rpt_20240115_001",
    "download_url": "/api/v1/reports/download/rpt_20240115_001",
    "expires_at": "2024-01-16T08:30:00Z"
}
```

### Download Report
```http
GET /api/v1/reports/download/{report_id}
Authorization: Bearer <token>
```

### Get Report Status
```http
GET /api/v1/reports/status/{report_id}
Authorization: Bearer <token>
```

---

## Branches API

### Get All Branches
```http
GET /api/v1/branches
Authorization: Bearer <token>
```

### Create Branch
```http
POST /api/v1/branches
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "فرع الرياض",
    "address": "شارع الملك فهد، الرياض",
    "phone": "+966112345678",
    "manager_name": "محمد العلي"
}
```

---

## Statistics API

### Dashboard Statistics
```http
GET /api/v1/stats/dashboard
Authorization: Bearer <token>
```

**Response:**
```json
{
    "success": true,
    "data": {
        "today": {
            "total_employees": 50,
            "present": 45,
            "absent": 5,
            "late": 3,
            "on_time": 42
        },
        "this_week": {
            "average_attendance": 92.5,
            "total_hours": 1800
        },
        "branches": [
            {
                "id": 1,
                "name": "الفرع الرئيسي",
                "present": 25,
                "total": 30
            }
        ]
    }
}
```

### Attendance Trends
```http
GET /api/v1/stats/trends
Authorization: Bearer <token>
```

**Query Parameters:**
- `period`: daily, weekly, monthly
- `branch_id` (optional)
- `date_from` (optional)
- `date_to` (optional)

---

## Webhooks

### Register Webhook
```http
POST /api/v1/webhooks
Authorization: Bearer <token>
Content-Type: application/json

{
    "url": "https://your-app.com/webhook/attendance",
    "events": ["attendance.check_in", "attendance.check_out"],
    "secret": "your_webhook_secret"
}
```

### Webhook Events

#### Attendance Check-in
```json
{
    "event": "attendance.check_in",
    "timestamp": "2024-01-15T08:30:00Z",
    "data": {
        "employee": {
            "id": 1,
            "name": "أحمد محمد",
            "branch_id": 1
        },
        "attendance": {
            "id": 123,
            "time_in": "08:30:00",
            "method": "camera",
            "confidence": 0.85
        }
    }
}
```

---

## Error Handling

### Standard Error Response
```json
{
    "success": false,
    "error": {
        "code": "EMPLOYEE_NOT_FOUND",
        "message": "الموظف غير موجود",
        "details": "Employee with ID 999 does not exist"
    },
    "timestamp": "2024-01-15T08:30:00Z"
}
```

### Common Error Codes
- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid input data
- `EMPLOYEE_NOT_FOUND`: Employee does not exist
- `ALREADY_CHECKED_IN`: Employee already checked in
- `CAMERA_ERROR`: Camera access or recognition error
- `RATE_LIMIT_EXCEEDED`: Too many requests

---

## Rate Limiting

- **Authentication**: 5 requests per minute
- **General API**: 100 requests per minute
- **Camera Recognition**: 10 requests per minute
- **Report Generation**: 5 requests per hour

**Rate Limit Headers:**
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

---

## SDK Examples

### Python SDK
```python
import requests

class AttendanceAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_employees(self, branch_id=None):
        params = {'branch_id': branch_id} if branch_id else {}
        response = requests.get(
            f'{self.base_url}/employees',
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def record_attendance(self, emp_id, action):
        data = {
            'emp_id': emp_id,
            'action': action,
            'method': 'api'
        }
        response = requests.post(
            f'{self.base_url}/attendance/record',
            headers=self.headers,
            json=data
        )
        return response.json()

# Usage
api = AttendanceAPI('http://localhost:5000/api/v1', 'your_token')
employees = api.get_employees()
result = api.record_attendance(1, 'check_in')
```

### JavaScript SDK
```javascript
class AttendanceAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getEmployees(branchId = null) {
        const params = branchId ? `?branch_id=${branchId}` : '';
        const response = await fetch(`${this.baseUrl}/employees${params}`, {
            headers: this.headers
        });
        return response.json();
    }
    
    async recordAttendance(empId, action) {
        const response = await fetch(`${this.baseUrl}/attendance/record`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                emp_id: empId,
                action: action,
                method: 'api'
            })
        });
        return response.json();
    }
}

// Usage
const api = new AttendanceAPI('http://localhost:5000/api/v1', 'your_token');
const employees = await api.getEmployees();
const result = await api.recordAttendance(1, 'check_in');
```

---

## Testing

### API Testing with curl
```bash
# Login
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Get employees
curl -X GET http://localhost:5000/api/v1/employees \
  -H "Authorization: Bearer YOUR_TOKEN"

# Record attendance
curl -X POST http://localhost:5000/api/v1/attendance/record \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"emp_id":1,"action":"check_in","method":"api"}'
```

### Postman Collection
A Postman collection is available for testing all API endpoints. Import the collection file `attendance_api.postman_collection.json` into Postman for easy testing.

---

For more information or support, please refer to the main documentation or contact the development team.
