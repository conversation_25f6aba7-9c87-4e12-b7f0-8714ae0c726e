#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحضور والانصراف - ملف التشغيل السريع
Attendance System - Quick Run File

هذا الملف يوفر طريقة سريعة لتشغيل النظام مع إعداد تلقائي
This file provides a quick way to run the system with automatic setup
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """طباعة شعار النظام - Print system banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🕐 نظام الحضور والانصراف المتطور 🕐                ║
    ║              Advanced Attendance System                      ║
    ║                                                              ║
    ║              مرحباً بك في النظام الذكي                      ║
    ║            Welcome to the Smart System                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """التحقق من إصدار Python - Check Python version"""
    print("🔍 التحقق من إصدار Python - Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("❌ Error: Python 3.8 or newer is required")
        print(f"   الإصدار الحالي - Current version: {sys.version}")
        return False
    
    print(f"✅ إصدار Python مناسب - Python version OK: {sys.version.split()[0]}")
    return True

def check_virtual_environment():
    """التحقق من البيئة الافتراضية - Check virtual environment"""
    print("\n🔍 التحقق من البيئة الافتراضية - Checking virtual environment...")
    
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ تعمل في بيئة افتراضية - Running in virtual environment")
        return True
    else:
        print("⚠️  تحذير: لا تعمل في بيئة افتراضية")
        print("⚠️  Warning: Not running in virtual environment")
        
        response = input("هل تريد المتابعة؟ (y/n) - Continue anyway? (y/n): ").lower()
        return response in ['y', 'yes', 'نعم']

def install_requirements():
    """تثبيت المتطلبات - Install requirements"""
    print("\n📦 تثبيت المتطلبات - Installing requirements...")
    
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        print("❌ requirements.txt file not found")
        return False
    
    try:
        # تحديث pip أولاً - Update pip first
        print("🔄 تحديث pip - Updating pip...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        
        # تثبيت المتطلبات - Install requirements
        print("📥 تثبيت المتطلبات - Installing packages...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                               check=True, capture_output=True, text=True)
        
        print("✅ تم تثبيت المتطلبات بنجاح - Requirements installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات - Error installing requirements:")
        print(f"   {e.stderr}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة - Create required directories"""
    print("\n📁 إنشاء المجلدات - Creating directories...")
    
    directories = [
        'static/uploads',
        'static/uploads/employees',
        'static/uploads/camera_captures',
        'static/uploads/reports',
        'logs',
        'data',
        'backups',
        'instance'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}")
    
    print("✅ تم إنشاء جميع المجلدات - All directories created")

def check_database():
    """التحقق من قاعدة البيانات - Check database"""
    print("\n🗄️  التحقق من قاعدة البيانات - Checking database...")
    
    if not os.path.exists('data/attendance.db'):
        print("⚠️  قاعدة البيانات غير موجودة - Database not found")
        
        response = input("هل تريد إنشاء قاعدة البيانات؟ (y/n) - Create database? (y/n): ").lower()
        if response in ['y', 'yes', 'نعم']:
            return initialize_database()
        else:
            print("❌ لا يمكن تشغيل النظام بدون قاعدة البيانات")
            print("❌ Cannot run system without database")
            return False
    else:
        print("✅ قاعدة البيانات موجودة - Database exists")
        return True

def initialize_database():
    """تهيئة قاعدة البيانات - Initialize database"""
    print("\n🔧 تهيئة قاعدة البيانات - Initializing database...")
    
    try:
        if os.path.exists('init_db.py'):
            result = subprocess.run([sys.executable, 'init_db.py'], 
                                   check=True, capture_output=True, text=True)
            print("✅ تم إنشاء قاعدة البيانات بنجاح - Database initialized successfully")
            return True
        else:
            print("❌ ملف init_db.py غير موجود")
            print("❌ init_db.py file not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات - Error initializing database:")
        print(f"   {e.stderr}")
        return False

def check_camera():
    """التحقق من الكاميرا - Check camera"""
    print("\n📷 التحقق من الكاميرا - Checking camera...")
    
    try:
        import cv2
        
        # محاولة فتح الكاميرا - Try to open camera
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ الكاميرا متاحة - Camera available")
            cap.release()
            return True
        else:
            print("⚠️  الكاميرا غير متاحة - Camera not available")
            print("   يمكن استخدام النظام بدون الكاميرا")
            print("   System can work without camera")
            return True
            
    except ImportError:
        print("⚠️  مكتبة OpenCV غير مثبتة - OpenCV not installed")
        print("   سيتم تعطيل ميزة الكاميرا - Camera feature will be disabled")
        return True
    except Exception as e:
        print(f"⚠️  خطأ في التحقق من الكاميرا - Camera check error: {e}")
        return True

def create_env_file():
    """إنشاء ملف البيئة - Create environment file"""
    print("\n⚙️  إعداد متغيرات البيئة - Setting up environment variables...")
    
    if not os.path.exists('.env'):
        env_content = """# نظام الحضور والانصراف - إعدادات البيئة
# Attendance System - Environment Settings

# إعدادات Flask الأساسية - Basic Flask Settings
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=9999
SECRET_KEY=your-secret-key-change-in-production

# إعدادات قاعدة البيانات - Database Settings
DATABASE_URL=sqlite:///data/attendance.db

# إعدادات الكاميرا - Camera Settings
CAMERA_ENABLED=True
CAMERA_INDEX=0
FACE_RECOGNITION_TOLERANCE=0.6

# إعدادات اللغة - Language Settings
DEFAULT_LANGUAGE=ar
LANGUAGES=ar,en

# إعدادات رفع الملفات - File Upload Settings
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# إعدادات الأمان - Security Settings
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# إعدادات الشركة - Company Settings
COMPANY_NAME=شركة التقنية المتقدمة
COMPANY_NAME_EN=Advanced Technology Company
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ تم إنشاء ملف .env - .env file created")
    else:
        print("✅ ملف .env موجود - .env file exists")

def run_system():
    """تشغيل النظام - Run the system"""
    print("\n🚀 تشغيل النظام - Starting the system...")
    print("=" * 60)
    print("🌐 النظام يعمل على - System running on:")
    print("   http://localhost:9999")
    print("   http://127.0.0.1:9999")
    print()
    print("👤 بيانات تسجيل الدخول الافتراضية - Default login credentials:")
    print("   المدير - Admin: admin / admin123")
    print("   الموارد البشرية - HR: hr / hr123")
    print("   المدير - Manager: manager / manager123")
    print()
    print("⌨️  اضغط Ctrl+C لإيقاف النظام - Press Ctrl+C to stop the system")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق - Run the application
        if os.path.exists('app.py'):
            subprocess.run([sys.executable, 'app.py'], check=True)
        else:
            print("❌ ملف app.py غير موجود")
            print("❌ app.py file not found")
            return False
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم - System stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ خطأ في تشغيل النظام - Error running system: {e}")
        return False

def main():
    """الوظيفة الرئيسية - Main function"""
    print_banner()
    
    # التحقق من المتطلبات الأساسية - Check basic requirements
    if not check_python_version():
        return False
    
    if not check_virtual_environment():
        return False
    
    # إعداد النظام - Setup system
    create_directories()
    create_env_file()
    
    # تثبيت المتطلبات - Install requirements
    print("\n📋 هل تريد تثبيت/تحديث المتطلبات؟")
    print("📋 Do you want to install/update requirements?")
    install_req = input("(y/n): ").lower()
    
    if install_req in ['y', 'yes', 'نعم']:
        if not install_requirements():
            return False
    
    # التحقق من قاعدة البيانات - Check database
    if not check_database():
        return False
    
    # التحقق من الكاميرا - Check camera
    check_camera()
    
    # تشغيل النظام - Run system
    print("\n🎯 جاهز للتشغيل - Ready to start!")
    input("اضغط Enter للمتابعة - Press Enter to continue...")
    
    return run_system()

if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n✅ تم إنهاء النظام بنجاح - System ended successfully")
        else:
            print("\n❌ حدث خطأ في النظام - System error occurred")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إلغاء العملية - Operation cancelled")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع - Unexpected error: {e}")
        sys.exit(1)
