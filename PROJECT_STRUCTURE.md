# 📁 هيكل المشروع - Project Structure

## نظرة عامة - Overview

يوضح هذا الملف الهيكل التنظيمي الكامل لمشروع نظام الحضور والانصراف، مع شرح وظيفة كل مجلد وملف.

This file shows the complete organizational structure of the attendance system project, explaining the function of each folder and file.

---

## 🏗️ الهيكل الأساسي - Basic Structure

```
attendance-system/
├── 📄 README.md                          # الوثيقة الرئيسية - Main documentation
├── 📄 requirements.txt                   # متطلبات Python - Python dependencies
├── 📄 .env.example                       # مثال على متغيرات البيئة - Environment variables example
├── 📄 .gitignore                         # ملفات مستبعدة من Git - Git ignored files
├── 📄 app.py                             # تطبيق Flask الرئيسي - Main Flask application
├── 📄 config.py                          # إعدادات التطبيق - Application configuration
├── 📄 wsgi.py                            # نقطة دخول WSGI - WSGI entry point
├── 📄 init_db.py                         # تهيئة قاعدة البيانات - Database initialization
├── 📄 babel.cfg                          # إعدادات الترجمة - Translation configuration
│
├── 📁 app/                               # مجلد التطبيق الرئيسي - Main application folder
│   ├── 📄 __init__.py                    # تهيئة التطبيق - Application initialization
│   ├── 📄 models.py                      # نماذج قاعدة البيانات - Database models
│   ├── 📄 forms.py                       # نماذج الويب - Web forms
│   ├── 📄 extensions.py                  # إضافات Flask - Flask extensions
│   │
│   ├── 📁 auth/                          # نظام المصادقة - Authentication system
│   │   ├── 📄 __init__.py
│   │   ├── 📄 routes.py                  # مسارات المصادقة - Authentication routes
│   │   ├── 📄 forms.py                   # نماذج المصادقة - Authentication forms
│   │   └── 📄 utils.py                   # أدوات المصادقة - Authentication utilities
│   │
│   ├── 📁 main/                          # الصفحات الرئيسية - Main pages
│   │   ├── 📄 __init__.py
│   │   ├── 📄 routes.py                  # المسارات الرئيسية - Main routes
│   │   └── 📄 forms.py                   # النماذج الرئيسية - Main forms
│   │
│   ├── 📁 employees/                     # إدارة الموظفين - Employee management
│   │   ├── 📄 __init__.py
│   │   ├── 📄 routes.py                  # مسارات الموظفين - Employee routes
│   │   ├── 📄 forms.py                   # نماذج الموظفين - Employee forms
│   │   └── 📄 utils.py                   # أدوات الموظفين - Employee utilities
│   │
│   ├── 📁 attendance/                    # نظام الحضور - Attendance system
│   │   ├── 📄 __init__.py
│   │   ├── 📄 routes.py                  # مسارات الحضور - Attendance routes
│   │   ├── 📄 forms.py                   # نماذج الحضور - Attendance forms
│   │   ├── 📄 camera.py                  # وحدة الكاميرا - Camera module
│   │   └── 📄 face_recognition.py        # التعرف على الوجه - Face recognition
│   │
│   ├── 📁 reports/                       # نظام التقارير - Reports system
│   │   ├── 📄 __init__.py
│   │   ├── 📄 routes.py                  # مسارات التقارير - Report routes
│   │   ├── 📄 generators.py              # مولدات التقارير - Report generators
│   │   ├── 📄 pdf_generator.py           # مولد PDF - PDF generator
│   │   └── 📄 excel_generator.py         # مولد Excel - Excel generator
│   │
│   ├── 📁 api/                           # واجهة برمجة التطبيقات - API interface
│   │   ├── 📄 __init__.py
│   │   ├── 📄 auth.py                    # مصادقة API - API authentication
│   │   ├── 📄 employees.py               # API الموظفين - Employees API
│   │   ├── 📄 attendance.py              # API الحضور - Attendance API
│   │   ├── 📄 reports.py                 # API التقارير - Reports API
│   │   └── 📄 utils.py                   # أدوات API - API utilities
│   │
│   └── 📁 admin/                         # لوحة الإدارة - Admin panel
│       ├── 📄 __init__.py
│       ├── 📄 routes.py                  # مسارات الإدارة - Admin routes
│       ├── 📄 forms.py                   # نماذج الإدارة - Admin forms
│       └── 📄 utils.py                   # أدوات الإدارة - Admin utilities
│
├── 📁 templates/                         # قوالب HTML - HTML templates
│   ├── 📄 base.html                      # القالب الأساسي - Base template
│   ├── 📄 index.html                     # الصفحة الرئيسية - Home page
│   │
│   ├── 📁 auth/                          # قوالب المصادقة - Authentication templates
│   │   ├── 📄 login.html                 # صفحة تسجيل الدخول - Login page
│   │   ├── 📄 register.html              # صفحة التسجيل - Registration page
│   │   └── 📄 reset_password.html        # إعادة تعيين كلمة المرور - Password reset
│   │
│   ├── 📁 employees/                     # قوالب الموظفين - Employee templates
│   │   ├── 📄 list.html                  # قائمة الموظفين - Employee list
│   │   ├── 📄 add.html                   # إضافة موظف - Add employee
│   │   ├── 📄 edit.html                  # تعديل موظف - Edit employee
│   │   └── 📄 profile.html               # ملف الموظف - Employee profile
│   │
│   ├── 📁 attendance/                    # قوالب الحضور - Attendance templates
│   │   ├── 📄 dashboard.html             # لوحة التحكم - Dashboard
│   │   ├── 📄 manual_entry.html          # الإدخال اليدوي - Manual entry
│   │   ├── 📄 camera.html                # واجهة الكاميرا - Camera interface
│   │   └── 📄 history.html               # تاريخ الحضور - Attendance history
│   │
│   ├── 📁 reports/                       # قوالب التقارير - Report templates
│   │   ├── 📄 generate.html              # إنشاء تقرير - Generate report
│   │   ├── 📄 view.html                  # عرض التقرير - View report
│   │   └── 📄 list.html                  # قائمة التقارير - Report list
│   │
│   └── 📁 admin/                         # قوالب الإدارة - Admin templates
│       ├── 📄 dashboard.html             # لوحة الإدارة - Admin dashboard
│       ├── 📄 settings.html              # الإعدادات - Settings
│       ├── 📄 users.html                 # إدارة المستخدمين - User management
│       └── 📄 branches.html              # إدارة الفروع - Branch management
│
├── 📁 static/                            # الملفات الثابتة - Static files
│   ├── 📁 css/                           # ملفات CSS - CSS files
│   │   ├── 📄 bootstrap.min.css          # إطار Bootstrap - Bootstrap framework
│   │   ├── 📄 main.css                   # الأنماط الرئيسية - Main styles
│   │   ├── 📄 rtl.css                    # أنماط RTL للعربية - RTL styles for Arabic
│   │   └── 📄 print.css                  # أنماط الطباعة - Print styles
│   │
│   ├── 📁 js/                            # ملفات JavaScript - JavaScript files
│   │   ├── 📄 jquery.min.js              # مكتبة jQuery - jQuery library
│   │   ├── 📄 bootstrap.min.js           # Bootstrap JS
│   │   ├── 📄 main.js                    # الوظائف الرئيسية - Main functions
│   │   ├── 📄 camera.js                  # وظائف الكاميرا - Camera functions
│   │   ├── 📄 attendance.js              # وظائف الحضور - Attendance functions
│   │   └── 📄 reports.js                 # وظائف التقارير - Report functions
│   │
│   ├── 📁 images/                        # الصور - Images
│   │   ├── 📄 logo.png                   # شعار النظام - System logo
│   │   ├── 📄 default-avatar.png         # صورة افتراضية - Default avatar
│   │   └── 📁 icons/                     # الأيقونات - Icons
│   │
│   ├── 📁 fonts/                         # الخطوط - Fonts
│   │   ├── 📄 arabic-font.woff2          # خط عربي - Arabic font
│   │   └── 📄 english-font.woff2         # خط إنجليزي - English font
│   │
│   └── 📁 uploads/                       # الملفات المرفوعة - Uploaded files
│       ├── 📁 employees/                 # صور الموظفين - Employee photos
│       ├── 📁 camera_captures/           # لقطات الكاميرا - Camera captures
│       └── 📁 reports/                   # التقارير المحفوظة - Saved reports
│
├── 📁 migrations/                        # ترحيل قاعدة البيانات - Database migrations
│   ├── 📄 alembic.ini                    # إعدادات Alembic - Alembic configuration
│   ├── 📄 env.py                         # بيئة الترحيل - Migration environment
│   ├── 📄 script.py.mako                 # قالب الترحيل - Migration template
│   └── 📁 versions/                      # إصدارات الترحيل - Migration versions
│
├── 📁 translations/                      # ملفات الترجمة - Translation files
│   ├── 📁 ar/                            # الترجمة العربية - Arabic translation
│   │   └── 📁 LC_MESSAGES/
│   │       ├── 📄 messages.po            # ملف الترجمة - Translation file
│   │       └── 📄 messages.mo            # ملف مترجم - Compiled translation
│   └── 📁 en/                            # الترجمة الإنجليزية - English translation
│       └── 📁 LC_MESSAGES/
│           ├── 📄 messages.po
│           └── 📄 messages.mo
│
├── 📁 utils/                             # الأدوات المساعدة - Utility functions
│   ├── 📄 __init__.py
│   ├── 📄 decorators.py                  # المزخرفات - Decorators
│   ├── 📄 helpers.py                     # الوظائف المساعدة - Helper functions
│   ├── 📄 validators.py                  # المدققات - Validators
│   ├── 📄 email.py                       # إرسال البريد - Email sending
│   ├── 📄 security.py                    # الأمان - Security utilities
│   └── 📄 date_helpers.py                # مساعدات التاريخ - Date helpers
│
├── 📁 tests/                             # الاختبارات - Tests
│   ├── 📄 __init__.py
│   ├── 📄 conftest.py                    # إعداد الاختبارات - Test configuration
│   ├── 📄 test_models.py                 # اختبار النماذج - Model tests
│   ├── 📄 test_auth.py                   # اختبار المصادقة - Authentication tests
│   ├── 📄 test_employees.py              # اختبار الموظفين - Employee tests
│   ├── 📄 test_attendance.py             # اختبار الحضور - Attendance tests
│   ├── 📄 test_reports.py                # اختبار التقارير - Report tests
│   ├── 📄 test_api.py                    # اختبار API - API tests
│   ├── 📄 test_security.py               # اختبار الأمان - Security tests
│   │
│   ├── 📁 fixtures/                      # بيانات الاختبار - Test fixtures
│   │   ├── 📄 users.json                 # بيانات المستخدمين - User data
│   │   ├── 📄 employees.json             # بيانات الموظفين - Employee data
│   │   └── 📄 attendance.json            # بيانات الحضور - Attendance data
│   │
│   └── 📁 performance/                   # اختبارات الأداء - Performance tests
│       ├── 📄 locustfile.py              # اختبار الحمولة - Load testing
│       └── 📄 stress_test.py             # اختبار الإجهاد - Stress testing
│
├── 📁 scripts/                           # النصوص المساعدة - Helper scripts
│   ├── 📄 backup.sh                      # نسخ احتياطي - Backup script
│   ├── 📄 restore.sh                     # استعادة - Restore script
│   ├── 📄 deploy.sh                      # النشر - Deployment script
│   ├── 📄 create_admin.py                # إنشاء مدير - Create admin
│   ├── 📄 import_employees.py            # استيراد موظفين - Import employees
│   └── 📄 cleanup.py                     # تنظيف الملفات - File cleanup
│
├── 📁 docs/                              # الوثائق - Documentation
│   ├── 📄 README.md                      # الوثيقة الرئيسية - Main documentation
│   ├── 📄 INSTALLATION_GUIDE.md          # دليل التثبيت - Installation guide
│   ├── 📄 USER_MANUAL.md                 # دليل المستخدم - User manual
│   ├── 📄 API_DOCUMENTATION.md           # وثائق API - API documentation
│   ├── 📄 DEPLOYMENT_GUIDE.md            # دليل النشر - Deployment guide
│   ├── 📄 SECURITY_GUIDE.md              # دليل الأمان - Security guide
│   ├── 📄 TESTING_GUIDE.md               # دليل الاختبار - Testing guide
│   ├── 📄 DATABASE_SCHEMA.sql            # مخطط قاعدة البيانات - Database schema
│   └── 📄 PROJECT_STRUCTURE.md           # هيكل المشروع - Project structure
│
├── 📁 logs/                              # ملفات السجل - Log files
│   ├── 📄 app.log                        # سجل التطبيق - Application log
│   ├── 📄 error.log                      # سجل الأخطاء - Error log
│   ├── 📄 access.log                     # سجل الوصول - Access log
│   └── 📄 security.log                   # سجل الأمان - Security log
│
├── 📁 data/                              # البيانات - Data
│   ├── 📄 attendance.db                  # قاعدة البيانات - Database file
│   ├── 📁 backups/                       # النسخ الاحتياطية - Backups
│   └── 📁 exports/                       # الملفات المصدرة - Exported files
│
└── 📁 docker/                            # إعدادات Docker - Docker configuration
    ├── 📄 Dockerfile                     # ملف Docker - Docker file
    ├── 📄 docker-compose.yml             # تركيب Docker - Docker compose
    ├── 📄 nginx.conf                     # إعداد Nginx - Nginx configuration
    └── 📁 scripts/                       # نصوص Docker - Docker scripts
        ├── 📄 entrypoint.sh               # نقطة الدخول - Entry point
        └── 📄 wait-for-it.sh              # انتظار الخدمات - Wait for services
```

---

## 📋 وصف المجلدات الرئيسية - Main Folder Descriptions

### 📁 app/ - مجلد التطبيق الرئيسي
يحتوي على جميع ملفات التطبيق الأساسية مقسمة حسب الوظيفة:
- **auth/**: نظام المصادقة وتسجيل الدخول
- **employees/**: إدارة بيانات الموظفين
- **attendance/**: نظام تسجيل الحضور والانصراف
- **reports/**: إنشاء وإدارة التقارير
- **api/**: واجهات برمجة التطبيقات RESTful
- **admin/**: لوحة الإدارة والإعدادات

### 📁 templates/ - قوالب HTML
قوالب Jinja2 لجميع صفحات الويب:
- تدعم اللغتين العربية والإنجليزية
- تصميم متجاوب يعمل على جميع الأجهزة
- قوالب منفصلة لكل وحدة وظيفية

### 📁 static/ - الملفات الثابتة
- **css/**: ملفات الأنماط مع دعم RTL للعربية
- **js/**: ملفات JavaScript للتفاعل
- **images/**: الصور والأيقونات
- **uploads/**: الملفات المرفوعة من المستخدمين

### 📁 tests/ - الاختبارات
اختبارات شاملة لجميع أجزاء النظام:
- اختبارات الوحدة للنماذج والوظائف
- اختبارات التكامل للـ API
- اختبارات الأداء والحمولة
- اختبارات الأمان

### 📁 docs/ - الوثائق
وثائق شاملة للمشروع:
- أدلة التثبيت والنشر
- دليل المستخدم
- وثائق API
- أدلة الأمان والاختبار

---

## 🔧 ملفات الإعداد - Configuration Files

### ملفات Python الأساسية
- **app.py**: نقطة دخول التطبيق الرئيسية
- **config.py**: إعدادات التطبيق لبيئات مختلفة
- **wsgi.py**: نقطة دخول خادم WSGI
- **requirements.txt**: متطلبات Python

### ملفات قاعدة البيانات
- **init_db.py**: تهيئة قاعدة البيانات
- **migrations/**: ملفات ترحيل قاعدة البيانات
- **DATABASE_SCHEMA.sql**: مخطط قاعدة البيانات الكامل

### ملفات الترجمة
- **babel.cfg**: إعدادات استخراج النصوص للترجمة
- **translations/**: ملفات الترجمة للعربية والإنجليزية

### ملفات النشر
- **docker/**: إعدادات Docker للنشر
- **scripts/**: نصوص النشر والصيانة

---

## 🚀 نصائح التطوير - Development Tips

### تنظيم الكود
1. **فصل الاهتمامات**: كل وحدة في مجلد منفصل
2. **أسماء واضحة**: أسماء ملفات ومجلدات وصفية
3. **التوثيق**: تعليقات وتوثيق شامل
4. **الاختبارات**: اختبارات لكل وحدة

### أفضل الممارسات
1. **استخدام البيئات الافتراضية**: لعزل المتطلبات
2. **متابعة PEP 8**: معايير كتابة Python
3. **إدارة الإصدارات**: استخدام Git بفعالية
4. **الأمان**: تطبيق أفضل ممارسات الأمان

### إضافة ميزات جديدة
1. إنشاء مجلد جديد في `app/`
2. إضافة المسارات في `routes.py`
3. إنشاء النماذج في `forms.py`
4. إضافة القوالب في `templates/`
5. كتابة الاختبارات في `tests/`

---

*هذا الهيكل قابل للتوسع ويمكن تعديله حسب احتياجات المشروع*

*This structure is scalable and can be modified according to project needs*
