#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحضور والانصراف - نسخة تعمل بضمان
Attendance System - Guaranteed Working Version

نسخة محسنة تتعامل مع جميع المشاكل المحتملة
Enhanced version that handles all potential issues
"""

import os
import sys
from datetime import datetime, date
from pathlib import Path

print("🚀 بدء تشغيل نظام الحضور والانصراف...")

# التأكد من وجود المجلدات المطلوبة
try:
    Path('static/uploads').mkdir(parents=True, exist_ok=True)
    Path('logs').mkdir(parents=True, exist_ok=True)
    print("✅ تم إنشاء المجلدات المطلوبة")
except Exception as e:
    print(f"⚠️  تحذير: {e}")

# استيراد المكتبات مع معالجة الأخطاء
try:
    from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Response
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, login_required, current_user, UserMixin, login_user, logout_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from werkzeug.utils import secure_filename
    import cv2
    import base64
    import numpy as np
    print("✅ تم استيراد جميع المكتبات بنجاح")
except ImportError as e:
    print(f"❌ مكتبة مفقودة: {e}")
    print("💡 قم بتشغيل: pip install flask flask-sqlalchemy flask-login opencv-python")
    sys.exit(1)

# إنشاء التطبيق
app = Flask(__name__)

# إعدادات التطبيق - استخدام مجلد المشروع الحالي
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///attendance_system.db'  # في المجلد الحالي
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'

print(f"📁 مسار قاعدة البيانات: {os.path.abspath('attendance_system.db')}")

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# إعداد مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    """نموذج المستخدم - User Model"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='admin')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Branch(db.Model):
    """نموذج الفرع - Branch Model"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    address = db.Column(db.Text)
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Employee(db.Model):
    """نموذج الموظف - Employee Model"""
    id = db.Column(db.Integer, primary_key=True)
    employee_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    job_title = db.Column(db.String(100))
    branch_id = db.Column(db.Integer, db.ForeignKey('branch.id'))
    hire_date = db.Column(db.Date)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    status = db.Column(db.String(20), default='active')
    photo_path = db.Column(db.String(200))  # مسار صورة الموظف
    created_at = db.Column(db.DateTime, default=datetime.now)

    # العلاقات
    branch = db.relationship('Branch', backref='employees')

class Attendance(db.Model):
    """نموذج الحضور - Attendance Model"""
    id = db.Column(db.Integer, primary_key=True)
    emp_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    time_in = db.Column(db.Time)
    time_out = db.Column(db.Time)
    status = db.Column(db.String(20), default='present')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # العلاقات
    employee = db.relationship('Employee', backref='attendance_records')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات
@app.route('/')
def index():
    """الصفحة الرئيسية - Home Page"""
    # إحصائيات للصفحة الرئيسية
    if current_user.is_authenticated:
        total_employees = Employee.query.filter_by(status='active').count()
        total_branches = Branch.query.filter_by(is_active=True).count()
        today_attendance = Attendance.query.filter_by(date=date.today()).count()
        return render_template('working_index.html',
                             total_employees=total_employees,
                             total_branches=total_branches,
                             today_attendance=today_attendance)
    
    return render_template('working_index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول - Login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user, remember=True)
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('working_login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج - Logout"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم - Dashboard"""
    # إحصائيات أساسية
    total_employees = Employee.query.filter_by(status='active').count()
    total_branches = Branch.query.filter_by(is_active=True).count()
    today_attendance = Attendance.query.filter_by(date=date.today()).count()
    
    # الموظفون الحاضرون اليوم
    present_today = db.session.query(Employee).join(Attendance).filter(
        Attendance.date == date.today(),
        Attendance.time_in.isnot(None)
    ).all()
    
    return render_template('working_dashboard.html',
                         total_employees=total_employees,
                         total_branches=total_branches,
                         today_attendance=today_attendance,
                         present_today=present_today)

@app.route('/employees')
@login_required
def employees_list():
    """قائمة الموظفين - Employees List"""
    employees = Employee.query.filter_by(status='active').all()
    return render_template('working_employees.html', employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    """إضافة موظف - Add Employee"""
    if request.method == 'POST':
        employee_code = request.form.get('employee_code')
        name = request.form.get('name')
        name_ar = request.form.get('name_ar')
        job_title = request.form.get('job_title')
        branch_id = request.form.get('branch_id')
        hire_date_str = request.form.get('hire_date')
        phone = request.form.get('phone')
        email = request.form.get('email')
        
        # تحويل تاريخ التوظيف
        hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date() if hire_date_str else None
        
        # إنشاء موظف جديد
        employee = Employee(
            employee_code=employee_code,
            name=name,
            name_ar=name_ar,
            job_title=job_title,
            branch_id=int(branch_id) if branch_id else None,
            hire_date=hire_date,
            phone=phone,
            email=email
        )
        
        try:
            db.session.add(employee)
            db.session.commit()
            flash('تم إضافة الموظف بنجاح', 'success')
            return redirect(url_for('employees_list'))
        except Exception as e:
            db.session.rollback()
            flash('خطأ في إضافة الموظف', 'error')
    
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('working_add_employee.html', branches=branches)

@app.route('/attendance/manual', methods=['GET', 'POST'])
@login_required
def manual_attendance():
    """تسجيل الحضور اليدوي - Manual Attendance"""
    if request.method == 'POST':
        emp_id = request.form.get('emp_id')
        action = request.form.get('action')
        notes = request.form.get('notes', '')
        
        employee = Employee.query.get_or_404(emp_id)
        today = date.today()
        
        # البحث عن سجل الحضور لليوم
        attendance = Attendance.query.filter_by(emp_id=emp_id, date=today).first()
        
        if not attendance:
            attendance = Attendance(emp_id=emp_id, date=today)
            db.session.add(attendance)
        
        # تحديث الحضور
        current_time = datetime.now().time()
        
        if action == 'check_in':
            attendance.time_in = current_time
            attendance.status = 'present'
            flash(f'تم تسجيل دخول {employee.name} بنجاح', 'success')
        elif action == 'check_out':
            attendance.time_out = current_time
            flash(f'تم تسجيل خروج {employee.name} بنجاح', 'success')
        
        if notes:
            attendance.notes = notes
        
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            flash('خطأ في تسجيل الحضور', 'error')
        
        return redirect(url_for('manual_attendance'))
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('working_attendance.html', employees=employees)

def init_database():
    """تهيئة قاعدة البيانات - Initialize Database"""
    try:
        print("🗄️  تهيئة قاعدة البيانات...")
        
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")
            
            # إنشاء مستخدم افتراضي
            if not User.query.filter_by(username='admin').first():
                admin = User(username='admin', role='admin')
                admin.set_password('admin123')
                db.session.add(admin)
                print("✅ تم إنشاء المستخدم الافتراضي: admin")
            
            # إنشاء فرع افتراضي
            if not Branch.query.first():
                branch = Branch(name='Main Branch', name_ar='الفرع الرئيسي', address='الرياض')
                db.session.add(branch)
                print("✅ تم إنشاء الفرع الافتراضي")
            
            # إنشاء موظف تجريبي
            if not Employee.query.first():
                employee = Employee(
                    employee_code='EMP001',
                    name='Ahmed Mohammed',
                    name_ar='أحمد محمد',
                    job_title='Software Developer',
                    branch_id=1,
                    hire_date=date.today()
                )
                db.session.add(employee)
                print("✅ تم إنشاء موظف تجريبي")
            
            db.session.commit()
            print("✅ تم حفظ البيانات الافتراضية")
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False
    
    return True

if __name__ == '__main__':
    print("🚀 نظام الحضور والانصراف - النسخة المضمونة")
    print("=" * 60)
    
    # تهيئة قاعدة البيانات
    if not init_database():
        print("❌ فشل في تهيئة قاعدة البيانات")
        sys.exit(1)
    
    print("🌐 النظام يعمل على: http://localhost:9999")
    print("👤 بيانات الدخول: admin / admin123")
    print("⌨️  اضغط Ctrl+C للإيقاف")
    print("=" * 60)
    
    try:
        app.run(host='0.0.0.0', port=9999, debug=True)
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من أن المنفذ 9999 غير مستخدم")
