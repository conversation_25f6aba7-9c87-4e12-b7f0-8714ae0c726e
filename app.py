#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحضور والانصراف - التطبيق الرئيسي
Attendance and Departure System - Main Application

هذا هو الملف الرئيسي لتطبيق Flask الذي يدير نظام الحضور والانصراف
This is the main Flask application file that manages the attendance system
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, login_required, current_user
from flask_babel import Babel, gettext, ngettext, lazy_gettext
from flask_wtf.csrf import CSRFProtect
from werkzeug.security import generate_password_hash, check_password_hash
# استيراد مكتبات الكاميرا والتعرف على الوجه (اختياري)
try:
    import cv2
    import face_recognition
    import numpy as np
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    print("⚠️  تحذير: مكتبات التعرف على الوجه غير مثبتة")
    print("⚠️  Warning: Face recognition libraries not installed")
    print("💡 لتثبيتها: pip install opencv-python face-recognition")
    print("💡 To install: pip install opencv-python face-recognition")

# إعداد التطبيق - Application Setup
app = Flask(__name__)

# إعداد الإعدادات - Configuration Setup
class Config:
    """إعدادات التطبيق - Application Configuration"""
    
    # الإعدادات الأساسية - Basic Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات - Database Settings
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///attendance.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات رفع الملفات - File Upload Settings
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'static/uploads'
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    
    # إعدادات اللغة - Language Settings
    LANGUAGES = ['ar', 'en']
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Riyadh'
    
    # إعدادات الكاميرا - Camera Settings
    CAMERA_INDEX = int(os.environ.get('CAMERA_INDEX', 0))
    FACE_RECOGNITION_TOLERANCE = float(os.environ.get('FACE_RECOGNITION_TOLERANCE', 0.6))

app.config.from_object(Config)

# تهيئة الإضافات - Initialize Extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = lazy_gettext('يرجى تسجيل الدخول للوصول لهذه الصفحة')
babel = Babel(app)
csrf = CSRFProtect(app)

# إعداد السجلات - Logging Setup
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = logging.FileHandler('logs/attendance.log', encoding='utf-8')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('تم بدء تشغيل نظام الحضور والانصراف - Attendance System startup')

# نماذج قاعدة البيانات - Database Models
from datetime import datetime, date
from flask_login import UserMixin

class User(UserMixin, db.Model):
    """نموذج المستخدم - User Model"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), unique=True)
    role = db.Column(db.String(20), default='admin')
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        """تعيين كلمة المرور - Set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور - Check password"""
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Branch(db.Model):
    """نموذج الفرع - Branch Model"""
    __tablename__ = 'branches'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    address = db.Column(db.Text)
    phone = db.Column(db.String(20))
    manager_name = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات - Relationships
    employees = db.relationship('Employee', backref='branch', lazy=True)
    
    def __repr__(self):
        return f'<Branch {self.name}>'

class Employee(db.Model):
    """نموذج الموظف - Employee Model"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    job_title = db.Column(db.String(100))
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    hire_date = db.Column(db.Date, nullable=False)
    image_path = db.Column(db.String(255))
    face_encoding = db.Column(db.Text)  # ترميز الوجه مخزن كنص - Face encoding stored as text
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات - Relationships
    attendance_records = db.relationship('Attendance', backref='employee', lazy=True)
    
    def get_display_name(self, language='ar'):
        """الحصول على الاسم للعرض - Get display name"""
        if language == 'ar' and self.name_ar:
            return self.name_ar
        return self.name
    
    def __repr__(self):
        return f'<Employee {self.employee_code}: {self.name}>'

class Attendance(db.Model):
    """نموذج الحضور - Attendance Model"""
    __tablename__ = 'attendance'
    
    id = db.Column(db.Integer, primary_key=True)
    emp_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    date = db.Column(db.Date, nullable=False, index=True)
    time_in = db.Column(db.Time)
    time_out = db.Column(db.Time)
    status = db.Column(db.String(20), default='present')
    attendance_method = db.Column(db.String(20), default='manual')
    camera_img = db.Column(db.String(255))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Attendance {self.emp_id} on {self.date}>'

# إعداد مدير تسجيل الدخول - Login Manager Setup
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# إعداد Babel للترجمة - Babel Setup for Translation
def get_locale():
    """تحديد اللغة المطلوبة - Determine required language"""
    # التحقق من اللغة المحددة في الجلسة - Check language set in session
    if 'language' in session:
        return session['language']

    # التحقق من اللغة المفضلة في المتصفح - Check browser preferred language
    return request.accept_languages.best_match(app.config['LANGUAGES']) or app.config['BABEL_DEFAULT_LOCALE']

# تسجيل دالة تحديد اللغة مع Babel (متوافق مع جميع الإصدارات)
try:
    # للإصدارات الحديثة من Flask-Babel (3.0+)
    babel.init_app(app, locale_selector=get_locale)
except (TypeError, AttributeError):
    try:
        # للإصدارات المتوسطة من Flask-Babel (2.0+)
        babel.localeselector(get_locale)
    except AttributeError:
        # للإصدارات القديمة جداً
        @babel.localeselector
        def locale_selector():
            return get_locale()

# المسارات الرئيسية - Main Routes
@app.route('/')
def index():
    """الصفحة الرئيسية - Home Page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم - Dashboard"""
    today = date.today()
    
    # إحصائيات اليوم - Today's statistics
    total_employees = Employee.query.filter_by(status='active').count()
    today_attendance = Attendance.query.filter_by(date=today).all()
    present_count = len([a for a in today_attendance if a.time_in])
    absent_count = total_employees - present_count
    
    # الموظفين الحاضرين اليوم - Today's present employees
    present_employees = []
    for attendance in today_attendance:
        if attendance.time_in:
            present_employees.append({
                'employee': attendance.employee,
                'time_in': attendance.time_in,
                'time_out': attendance.time_out,
                'status': attendance.status
            })
    
    return render_template('dashboard.html',
                         total_employees=total_employees,
                         present_count=present_count,
                         absent_count=absent_count,
                         present_employees=present_employees)

@app.route('/set_language/<language>')
def set_language(language=None):
    """تعيين اللغة - Set Language"""
    if language in app.config['LANGUAGES']:
        session['language'] = language
    return redirect(request.referrer or url_for('index'))

# مسارات المصادقة - Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول - Login"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash(gettext('يرجى إدخال اسم المستخدم وكلمة المرور'), 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            from flask_login import login_user
            login_user(user, remember=True)
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            flash(gettext('تم تسجيل الدخول بنجاح'), 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash(gettext('اسم المستخدم أو كلمة المرور غير صحيحة'), 'error')
    
    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج - Logout"""
    from flask_login import logout_user
    logout_user()
    flash(gettext('تم تسجيل الخروج بنجاح'), 'info')
    return redirect(url_for('index'))

# مسارات الموظفين - Employee Routes
@app.route('/employees')
@login_required
def employees_list():
    """قائمة الموظفين - Employees List"""
    page = request.args.get('page', 1, type=int)
    employees = Employee.query.filter_by(status='active').paginate(
        page=page, per_page=20, error_out=False)
    return render_template('employees/list.html', employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    """إضافة موظف جديد - Add New Employee"""
    if request.method == 'POST':
        employee_code = request.form.get('employee_code')
        name = request.form.get('name')
        name_ar = request.form.get('name_ar')
        job_title = request.form.get('job_title')
        branch_id = request.form.get('branch_id')
        phone = request.form.get('phone')
        email = request.form.get('email')
        hire_date = datetime.strptime(request.form.get('hire_date'), '%Y-%m-%d').date()
        
        # التحقق من عدم تكرار رقم الموظف - Check employee code uniqueness
        if Employee.query.filter_by(employee_code=employee_code).first():
            flash(gettext('رقم الموظف موجود مسبقاً'), 'error')
            return render_template('employees/add.html', branches=Branch.query.filter_by(is_active=True).all())
        
        employee = Employee(
            employee_code=employee_code,
            name=name,
            name_ar=name_ar,
            job_title=job_title,
            branch_id=branch_id,
            phone=phone,
            email=email,
            hire_date=hire_date
        )
        
        # معالجة رفع الصورة - Handle image upload
        if 'photo' in request.files:
            photo = request.files['photo']
            if photo.filename:
                # حفظ الصورة ومعالجة ترميز الوجه - Save image and process face encoding
                filename = f"emp_{employee_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                photo_path = os.path.join(app.config['UPLOAD_FOLDER'], 'employees', filename)
                
                # إنشاء المجلد إذا لم يكن موجوداً - Create directory if not exists
                os.makedirs(os.path.dirname(photo_path), exist_ok=True)
                photo.save(photo_path)
                
                employee.image_path = photo_path
                
                # معالجة ترميز الوجه - Process face encoding
                if FACE_RECOGNITION_AVAILABLE:
                    try:
                        image = face_recognition.load_image_file(photo_path)
                        face_encodings = face_recognition.face_encodings(image)
                        if face_encodings:
                            employee.face_encoding = ','.join(map(str, face_encodings[0]))
                            flash(gettext('تم تسجيل الوجه بنجاح'), 'success')
                        else:
                            flash(gettext('لم يتم العثور على وجه في الصورة'), 'warning')
                    except Exception as e:
                        app.logger.error(f'خطأ في معالجة الوجه: {str(e)}')
                        flash(gettext('خطأ في معالجة الصورة'), 'error')
                else:
                    flash(gettext('ميزة التعرف على الوجه غير متاحة - تم حفظ الصورة فقط'), 'info')
        
        db.session.add(employee)
        db.session.commit()
        
        flash(gettext('تم إضافة الموظف بنجاح'), 'success')
        return redirect(url_for('employees_list'))
    
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('employees/add.html', branches=branches)

# مسارات الحضور - Attendance Routes
@app.route('/attendance/manual', methods=['GET', 'POST'])
@login_required
def manual_attendance():
    """تسجيل الحضور اليدوي - Manual Attendance Recording"""
    if request.method == 'POST':
        emp_id = request.form.get('emp_id')
        action = request.form.get('action')  # check_in or check_out
        notes = request.form.get('notes', '')
        
        employee = Employee.query.get_or_404(emp_id)
        today = date.today()
        
        # البحث عن سجل الحضور لليوم - Find today's attendance record
        attendance = Attendance.query.filter_by(emp_id=emp_id, date=today).first()
        
        if not attendance:
            attendance = Attendance(emp_id=emp_id, date=today)
            db.session.add(attendance)
        
        if action == 'check_in':
            if attendance.time_in:
                flash(gettext('الموظف مسجل دخول مسبقاً اليوم'), 'warning')
            else:
                attendance.time_in = datetime.now().time()
                attendance.status = 'present'
                attendance.attendance_method = 'manual'
                attendance.notes = notes
                flash(gettext('تم تسجيل الدخول بنجاح'), 'success')
        
        elif action == 'check_out':
            if not attendance.time_in:
                flash(gettext('يجب تسجيل الدخول أولاً'), 'error')
            elif attendance.time_out:
                flash(gettext('الموظف مسجل خروج مسبقاً اليوم'), 'warning')
            else:
                attendance.time_out = datetime.now().time()
                if notes:
                    attendance.notes = (attendance.notes or '') + f' | خروج: {notes}'
                flash(gettext('تم تسجيل الخروج بنجاح'), 'success')
        
        db.session.commit()
        return redirect(url_for('manual_attendance'))
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('attendance/manual.html', employees=employees)

# معالج الأخطاء - Error Handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

# إنشاء الجداول والبيانات الأولية - Create Tables and Initial Data
def create_initial_data():
    """إنشاء البيانات الأولية - Create Initial Data"""
    db.create_all()
    
    # إنشاء مستخدم مدير افتراضي - Create default admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', email='<EMAIL>', role='admin')
        admin.set_password('admin123')
        db.session.add(admin)
    
    # إنشاء فرع افتراضي - Create default branch
    if not Branch.query.filter_by(name='الفرع الرئيسي').first():
        main_branch = Branch(
            name='الفرع الرئيسي',
            name_ar='الفرع الرئيسي',
            address='الرياض، المملكة العربية السعودية',
            manager_name='مدير النظام'
        )
        db.session.add(main_branch)
    
    db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        create_initial_data()
    
    # تشغيل التطبيق - Run Application
    app.run(
        host=os.environ.get('FLASK_HOST', '0.0.0.0'),
        port=int(os.environ.get('FLASK_PORT', 9999)),
        debug=os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    )
