#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 نظام الحضور الذكي المبسط - Simple Smart Attendance System
نسخة مبسطة تعمل بدون مكتبات التعرف على الوجوه المعقدة
"""

import os
import sys
import cv2
import numpy as np
import json
import time
from datetime import datetime, date
from flask import Flask, render_template, request, redirect, url_for, flash, session, Response, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSRFProtect
from werkzeug.security import generate_password_hash, check_password_hash

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'smart_attendance_secret_key_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///smart_attendance.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'

# إنشاء مجلد الصور
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# إعداد حماية CSRF
csrf = CSRFProtect(app)

# استثناءات CSRF للمسارات الخاصة
@csrf.exempt
def csrf_exempt_routes():
    """مسارات مستثناة من CSRF"""
    exempt_routes = [
        '/toggle_smart_mode',
        '/toggle_auto_detection',
        '/smart_camera_feed',
        '/camera_feed'
    ]
    return exempt_routes

# إعداد مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# متغيرات النظام الذكي
smart_mode_active = False
auto_detection_enabled = False
last_detection_time = {}
detection_cooldown = 30  # ثواني

# نماذج قاعدة البيانات
class Branch(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    branch_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    address = db.Column(db.Text)
    address_ar = db.Column(db.Text)
    city = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    manager_name = db.Column(db.String(100))
    manager_phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # علاقة مع الموظفين
    employees = db.relationship('Employee', backref='branch', lazy=True)

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    position = db.Column(db.String(100))
    department = db.Column(db.String(100))
    branch_id = db.Column(db.Integer, db.ForeignKey('branch.id'), default=1)
    photo_path = db.Column(db.String(200))
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.now)

class SmartAttendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    emp_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    time_in = db.Column(db.Time)
    time_out = db.Column(db.Time)
    detection_method = db.Column(db.String(50), default='smart_camera')
    confidence = db.Column(db.Float, default=0.0)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    employee = db.relationship('Employee', backref='smart_attendance_records')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# دوال النظام الذكي المبسط
def simple_face_detection(frame):
    """كشف الوجوه البسيط باستخدام OpenCV"""
    try:
        # تحويل إلى رمادي
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # كاشف الوجوه
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # كشف الوجوه
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        return faces
    except Exception as e:
        print(f"خطأ في كشف الوجوه: {e}")
        return []

def simulate_employee_recognition(faces):
    """محاكاة التعرف على الموظفين (للعرض التوضيحي)"""
    if len(faces) == 0:
        return []
    
    # قائمة الموظفين للمحاكاة
    employees = Employee.query.filter_by(status='active').all()
    if not employees:
        return []
    
    recognized = []
    for i, (x, y, w, h) in enumerate(faces):
        if i < len(employees):
            employee = employees[i % len(employees)]
            confidence = np.random.uniform(0.75, 0.95)  # محاكاة الثقة
            
            recognized.append({
                'employee': employee,
                'confidence': confidence,
                'location': (x, y, w, h)
            })
    
    return recognized

def can_register_attendance(employee_id):
    """فحص إمكانية تسجيل الحضور"""
    global last_detection_time
    
    current_time = time.time()
    
    if employee_id in last_detection_time:
        time_diff = current_time - last_detection_time[employee_id]
        if time_diff < detection_cooldown:
            return False, f"انتظر {detection_cooldown - int(time_diff)} ثانية"
    
    last_detection_time[employee_id] = current_time
    return True, "جاهز للتسجيل"

def register_smart_attendance(employee, confidence):
    """تسجيل الحضور الذكي"""
    try:
        can_register, message = can_register_attendance(employee.id)
        if not can_register:
            return False, message
        
        today = date.today()
        attendance = SmartAttendance.query.filter_by(emp_id=employee.id, date=today).first()
        
        current_time = datetime.now().time()
        
        if not attendance or not attendance.time_in:
            # تسجيل دخول
            if not attendance:
                attendance = SmartAttendance(emp_id=employee.id, date=today)
                db.session.add(attendance)
            
            attendance.time_in = current_time
            attendance.confidence = confidence
            attendance.notes = f'تسجيل دخول ذكي - ثقة: {confidence:.1%}'
            
            db.session.commit()
            return True, f"تم تسجيل دخول {employee.name_ar or employee.name} تلقائياً"
            
        elif attendance.time_in and not attendance.time_out:
            # تسجيل خروج
            attendance.time_out = current_time
            attendance.notes += f' | تسجيل خروج ذكي - ثقة: {confidence:.1%}'
            
            db.session.commit()
            return True, f"تم تسجيل خروج {employee.name_ar or employee.name} تلقائياً"
        
        else:
            return False, "تم تسجيل الدخول والخروج لليوم"
            
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في التسجيل: {str(e)}"

# المسارات
@app.route('/')
def index():
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
@csrf.exempt
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    employees_count = Employee.query.filter_by(status='active').count()
    today_attendance = SmartAttendance.query.filter_by(date=date.today()).count()
    
    return render_template('simple_smart_dashboard.html', 
                         employees_count=employees_count,
                         today_attendance=today_attendance)

@app.route('/smart_attendance')
@login_required
def smart_attendance():
    employees = Employee.query.filter_by(status='active').all()
    return render_template('simple_smart_attendance.html', employees=employees)

@app.route('/smart_camera_feed')
@csrf.exempt
@login_required
def smart_camera_feed():
    """تدفق الكاميرا الذكي"""
    def generate():
        global smart_mode_active, auto_detection_enabled
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame = cv2.flip(frame, 1)
                
                # كشف الوجوه كل 15 إطار
                if smart_mode_active and frame_count % 15 == 0:
                    faces = simple_face_detection(frame)
                    
                    if auto_detection_enabled and len(faces) > 0:
                        with app.app_context():
                            recognized = simulate_employee_recognition(faces)

                            for recognition in recognized:
                                employee = recognition['employee']
                                confidence = recognition['confidence']
                                x, y, w, h = recognition['location']

                                # رسم مربع حول الوجه
                                cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)

                                # عرض اسم الموظف
                                name = employee.name_ar or employee.name
                                text = f"{name} ({confidence:.1%})"
                                cv2.putText(frame, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                                # محاولة التسجيل
                                if confidence > 0.8:
                                    success, message = register_smart_attendance(employee, confidence)
                                    if success:
                                        print(f"✅ {message}")
                                        cv2.circle(frame, (x+w-20, y+20), 10, (0, 255, 0), -1)
                
                # إضافة معلومات على الشاشة
                status_color = (0, 255, 0) if smart_mode_active else (0, 0, 255)
                cv2.putText(frame, f"Smart Mode: {'ON' if smart_mode_active else 'OFF'}", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
                
                auto_color = (0, 255, 0) if auto_detection_enabled else (0, 0, 255)
                cv2.putText(frame, f"Auto Detection: {'ON' if auto_detection_enabled else 'OFF'}", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, auto_color, 2)
                
                frame_count += 1
                
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                frame_bytes = buffer.tobytes()
                
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
        
        except Exception as e:
            print(f"خطأ في الكاميرا الذكية: {e}")
        finally:
            cap.release()
    
    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/toggle_smart_mode', methods=['POST'])
@csrf.exempt
@login_required
def toggle_smart_mode():
    global smart_mode_active
    data = request.get_json()
    smart_mode_active = data.get('enabled', False)
    
    message = "تم تفعيل النمط الذكي" if smart_mode_active else "تم إلغاء تفعيل النمط الذكي"
    return jsonify({'success': True, 'message': message, 'smart_mode_active': smart_mode_active})

@app.route('/toggle_auto_detection', methods=['POST'])
@csrf.exempt
@login_required
def toggle_auto_detection():
    global auto_detection_enabled
    data = request.get_json()
    auto_detection_enabled = data.get('enabled', False)
    
    message = "تم تفعيل الكشف التلقائي" if auto_detection_enabled else "تم إلغاء تفعيل الكشف التلقائي"
    return jsonify({'success': True, 'message': message, 'auto_detection_enabled': auto_detection_enabled})

@app.route('/attendance_log')
@login_required
def attendance_log():
    today = date.today()
    records = SmartAttendance.query.filter_by(date=today).order_by(SmartAttendance.created_at.desc()).all()
    
    log_data = []
    for record in records:
        log_data.append({
            'employee_name': record.employee.name_ar or record.employee.name,
            'time_in': record.time_in.strftime('%H:%M') if record.time_in else None,
            'time_out': record.time_out.strftime('%H:%M') if record.time_out else None,
            'confidence': f"{record.confidence:.1%}" if record.confidence else "N/A",
            'notes': record.notes or ""
        })
    
    return jsonify({'success': True, 'records': log_data})

# ===== إدارة الموظفين =====
@app.route('/employees')
@login_required
def employees_list():
    """قائمة الموظفين"""
    employees = Employee.query.filter_by(status='active').all()
    return render_template('simple_employees_list.html', employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def add_employee():
    """إضافة موظف جديد"""
    if request.method == 'POST':
        try:
            # استلام البيانات
            emp_id = request.form.get('emp_id')
            name = request.form.get('name')
            name_ar = request.form.get('name_ar')
            email = request.form.get('email')
            phone = request.form.get('phone')
            position = request.form.get('position')
            department = request.form.get('department')

            # التحقق من عدم تكرار رقم الموظف
            if Employee.query.filter_by(employee_code=emp_id).first():
                flash('رقم الموظف موجود مسبقاً', 'error')
                return redirect(url_for('add_employee'))

            # إنشاء الموظف الجديد
            branch_id = request.form.get('branch_id', 1)
            employee = Employee(
                employee_code=emp_id,
                name=name,
                name_ar=name_ar,
                email=email,
                phone=phone,
                position=position,
                department=department,
                branch_id=int(branch_id)
            )

            # معالجة الصورة
            photo_path = None

            # التحقق من وجود صورة ملتقطة
            captured_photo_data = request.form.get('captured_photo_data')
            if captured_photo_data:
                try:
                    import base64
                    import os
                    from datetime import datetime
                    import uuid

                    # إنشاء مجلد الصور
                    upload_folder = 'static/employee_photos'
                    if not os.path.exists(upload_folder):
                        os.makedirs(upload_folder)

                    # فصل البيانات عن الترويسة
                    header, data = captured_photo_data.split(',', 1)
                    image_data = base64.b64decode(data)

                    # إنشاء اسم فريد للصورة
                    filename = f"emp_{emp_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    filepath = os.path.join(upload_folder, filename)

                    # حفظ الصورة
                    with open(filepath, 'wb') as f:
                        f.write(image_data)

                    photo_path = filepath

                except Exception as e:
                    print(f"خطأ في حفظ الصورة الملتقطة: {e}")

            # التحقق من رفع صورة من الجهاز
            elif 'photo' in request.files:
                file = request.files['photo']
                if file and file.filename != '':
                    try:
                        import os
                        from datetime import datetime

                        # إنشاء مجلد الصور
                        upload_folder = 'static/employee_photos'
                        if not os.path.exists(upload_folder):
                            os.makedirs(upload_folder)

                        # إنشاء اسم فريد للصورة
                        file_extension = file.filename.rsplit('.', 1)[1].lower()
                        filename = f"emp_{emp_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                        filepath = os.path.join(upload_folder, filename)

                        # حفظ الصورة
                        file.save(filepath)
                        photo_path = filepath

                    except Exception as e:
                        print(f"خطأ في حفظ الصورة المرفوعة: {e}")

            # إضافة مسار الصورة للموظف
            if photo_path:
                employee.photo_path = photo_path

            db.session.add(employee)
            db.session.commit()

            flash('تم إضافة الموظف بنجاح', 'success')
            return redirect(url_for('employees_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة الموظف: {str(e)}', 'error')

    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('add_employee_simple.html', branches=branches)

@app.route('/employees/edit/<int:emp_id>', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def edit_employee(emp_id):
    """تعديل موظف"""
    employee = Employee.query.get_or_404(emp_id)

    if request.method == 'POST':
        try:
            employee.name = request.form.get('name')
            employee.name_ar = request.form.get('name_ar')
            employee.email = request.form.get('email')
            employee.phone = request.form.get('phone')
            employee.position = request.form.get('position')
            employee.department = request.form.get('department')
            employee.status = request.form.get('status', 'active')
            branch_id = request.form.get('branch_id')
            if branch_id:
                employee.branch_id = int(branch_id)

            db.session.commit()
            flash('تم تحديث بيانات الموظف بنجاح', 'success')
            return redirect(url_for('view_employee', emp_id=emp_id))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث الموظف: {str(e)}', 'error')

    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('edit_employee.html', employee=employee, branches=branches)

@app.route('/employees/view/<int:emp_id>')
@login_required
def view_employee(emp_id):
    """عرض تفاصيل موظف"""
    employee = Employee.query.get_or_404(emp_id)
    return render_template('view_employee.html', employee=employee)

@app.route('/employees/delete/<int:emp_id>', methods=['POST'])
@csrf.exempt
@login_required
def delete_employee(emp_id):
    """حذف موظف"""
    try:
        employee = Employee.query.get_or_404(emp_id)
        employee.status = 'inactive'  # حذف منطقي
        db.session.commit()
        flash('تم حذف الموظف بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الموظف: {str(e)}', 'error')

    return redirect(url_for('employees_list'))

# ===== مسارات إضافية للموظفين =====
@app.route('/employee_photo_feed')
@csrf.exempt
@login_required
def employee_photo_feed():
    """تدفق الكاميرا لالتقاط صورة الموظف"""
    def generate():
        if not FACE_RECOGNITION_AVAILABLE:
            # إرسال صورة افتراضية
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' +
                   b'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5DYXB0dXJlIFBob3RvPC90ZXh0Pjwvc3ZnPg==' +
                   b'\r\n')
            return

        cap = cv2.VideoCapture(0)
        try:
            while True:
                success, frame = cap.read()
                if not success:
                    break

                # تحسين جودة الصورة
                frame = cv2.flip(frame, 1)

                # إضافة إطار للوجه
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                face_locations = face_recognition.face_locations(gray)

                for (top, right, bottom, left) in face_locations:
                    cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 2)
                    cv2.putText(frame, 'Ready to Capture', (left, top-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                ret, buffer = cv2.imencode('.jpg', frame)
                frame = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
        finally:
            cap.release()

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture_employee_photo', methods=['POST'])
@csrf.exempt
@login_required
def capture_employee_photo():
    """التقاط صورة الموظف"""
    try:
        if not FACE_RECOGNITION_AVAILABLE:
            return jsonify({'success': False, 'message': 'الكاميرا غير متاحة'})

        cap = cv2.VideoCapture(0)
        success, frame = cap.read()
        cap.release()

        if success:
            # حفظ الصورة مؤقتاً
            import os
            if not os.path.exists('static/employee_photos'):
                os.makedirs('static/employee_photos')

            photo_path = f'static/employee_photos/temp_photo.jpg'
            cv2.imwrite(photo_path, frame)

            return jsonify({'success': True, 'message': 'تم التقاط الصورة بنجاح', 'photo_path': photo_path})
        else:
            return jsonify({'success': False, 'message': 'فشل في التقاط الصورة'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

# ===== إدارة الحضور =====
@app.route('/attendance')
@login_required
def attendance_list():
    """قائمة الحضور"""
    today = date.today()
    records = SmartAttendance.query.filter_by(date=today).all()
    return render_template('working_attendance.html', records=records, today=today)

@app.route('/attendance/manual', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def manual_attendance():
    """تسجيل الحضور اليدوي"""
    if request.method == 'POST':
        try:
            emp_id = request.form.get('emp_id')
            action = request.form.get('action')  # check_in or check_out
            notes = request.form.get('notes', '')

            employee = Employee.query.get_or_404(emp_id)
            today = date.today()

            # البحث عن سجل الحضور لليوم
            record = SmartAttendance.query.filter_by(emp_id=emp_id, date=today).first()

            if not record:
                record = SmartAttendance(
                    emp_id=emp_id,
                    date=today,
                    detection_method='manual'
                )
                db.session.add(record)

            if action == 'check_in':
                if record.time_in:
                    flash('الموظف مسجل دخول مسبقاً اليوم', 'warning')
                else:
                    record.time_in = datetime.now().time()
                    record.notes = notes
                    flash('تم تسجيل الدخول بنجاح', 'success')

            elif action == 'check_out':
                if not record.time_in:
                    flash('يجب تسجيل الدخول أولاً', 'error')
                elif record.time_out:
                    flash('الموظف مسجل خروج مسبقاً اليوم', 'warning')
                else:
                    record.time_out = datetime.now().time()
                    if notes:
                        record.notes = (record.notes or '') + f' | خروج: {notes}'
                    flash('تم تسجيل الخروج بنجاح', 'success')

            db.session.commit()
            return redirect(url_for('manual_attendance'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في التسجيل: {str(e)}', 'error')

    employees = Employee.query.filter_by(status='active').all()
    print(f"عدد الموظفين النشطين: {len(employees)}")
    for emp in employees:
        print(f"موظف: {emp.id} - {emp.employee_code} - {emp.name_ar or emp.name}")

    # إحصائيات اليوم
    today = date.today()
    today_checkins = SmartAttendance.query.filter(
        SmartAttendance.date == today,
        SmartAttendance.time_in.isnot(None)
    ).count()

    today_checkouts = SmartAttendance.query.filter(
        SmartAttendance.date == today,
        SmartAttendance.time_out.isnot(None)
    ).count()

    active_employees = SmartAttendance.query.filter(
        SmartAttendance.date == today,
        SmartAttendance.time_in.isnot(None),
        SmartAttendance.time_out.is_(None)
    ).count()

    total_employees = Employee.query.filter_by(status='active').count()

    return render_template('simple_manual_attendance.html',
                         employees=employees,
                         today_checkins=today_checkins,
                         today_checkouts=today_checkouts,
                         active_employees=active_employees,
                         total_employees=total_employees)

@app.route('/attendance/employee_status/<int:emp_id>')
@login_required
def employee_status(emp_id):
    """التحقق من حالة الموظف الحالية"""
    today = date.today()
    record = SmartAttendance.query.filter_by(
        emp_id=emp_id,
        date=today
    ).first()

    if record:
        if record.time_in and not record.time_out:
            status = 'checked_in'
            message = f'متواجد منذ {record.time_in.strftime("%H:%M")}'
        elif record.time_in and record.time_out:
            status = 'checked_out'
            message = f'انصرف في {record.time_out.strftime("%H:%M")}'
        else:
            status = 'unknown'
            message = 'حالة غير محددة'
    else:
        status = 'not_checked_in'
        message = 'لم يسجل حضور اليوم'

    return {
        'status': status,
        'message': message,
        'can_check_in': status in ['not_checked_in', 'checked_out'],
        'can_check_out': status == 'checked_in'
    }

@app.route('/attendance/camera')
@login_required
def camera_attendance():
    """تسجيل الحضور بالكاميرا"""
    employees = Employee.query.filter_by(status='active').all()
    return render_template('working_camera_attendance.html', employees=employees)

# ===== التقارير =====
@app.route('/reports')
@login_required
def reports():
    """مركز التقارير"""
    # إحصائيات سريعة
    total_employees = Employee.query.filter_by(status='active').count()
    total_branches = Branch.query.filter_by(is_active=True).count()
    today = date.today()
    today_attendance = SmartAttendance.query.filter_by(date=today).count()
    active_now = SmartAttendance.query.filter(
        SmartAttendance.date == today,
        SmartAttendance.time_in.isnot(None),
        SmartAttendance.time_out.is_(None)
    ).count()

    employees = Employee.query.filter_by(status='active').all()
    branches = Branch.query.filter_by(is_active=True).all()

    return render_template('reports_dashboard.html',
                         total_employees=total_employees,
                         total_branches=total_branches,
                         today_attendance=today_attendance,
                         active_now=active_now,
                         employees=employees,
                         branches=branches)

@app.route('/reports/daily')
@login_required
def daily_report():
    """تقرير يومي"""
    today = date.today()
    records = SmartAttendance.query.filter_by(date=today).all()
    return render_template('working_daily_report.html', records=records, today=today)

@app.route('/reports/monthly')
@login_required
def monthly_report():
    """تقرير شهري"""
    from datetime import datetime
    current_month = datetime.now().month
    current_year = datetime.now().year

    records = SmartAttendance.query.filter(
        db.extract('month', SmartAttendance.date) == current_month,
        db.extract('year', SmartAttendance.date) == current_year
    ).all()

    return render_template('working_monthly_report.html', records=records)

@app.route('/reports/employee/<int:emp_id>')
@login_required
def employee_report(emp_id):
    """تقرير موظف محدد"""
    employee = Employee.query.get_or_404(emp_id)
    from datetime import datetime, timedelta

    # آخر 30 يوم
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)

    records = SmartAttendance.query.filter(
        SmartAttendance.emp_id == emp_id,
        SmartAttendance.date >= start_date,
        SmartAttendance.date <= end_date
    ).order_by(SmartAttendance.date.desc()).all()

    return render_template('working_employee_report.html',
                         employee=employee, records=records,
                         start_date=start_date, end_date=end_date)

@app.route('/reports/branch/<int:branch_id>')
@login_required
def branch_report(branch_id):
    """تقرير فرع محدد"""
    branch = Branch.query.get_or_404(branch_id)
    from datetime import datetime, timedelta

    # آخر 30 يوم
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)

    # موظفي الفرع
    employees = Employee.query.filter_by(branch_id=branch_id, status='active').all()
    employee_ids = [emp.id for emp in employees]

    # سجلات الحضور للفرع
    records = SmartAttendance.query.filter(
        SmartAttendance.emp_id.in_(employee_ids),
        SmartAttendance.date >= start_date,
        SmartAttendance.date <= end_date
    ).order_by(SmartAttendance.date.desc()).all()

    return render_template('branch_report.html',
                         branch=branch, employees=employees, records=records,
                         start_date=start_date, end_date=end_date)

@app.route('/reports/custom')
@login_required
def custom_report():
    """تقرير مخصص"""
    from datetime import datetime

    start_date_str = request.args.get('start')
    end_date_str = request.args.get('end')

    if start_date_str and end_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        records = SmartAttendance.query.filter(
            SmartAttendance.date >= start_date,
            SmartAttendance.date <= end_date
        ).order_by(SmartAttendance.date.desc()).all()

        return render_template('custom_report.html',
                             records=records, start_date=start_date, end_date=end_date)

    return redirect(url_for('reports'))

# ===== التقاط الصور =====
@app.route('/camera_capture')
@login_required
def camera_capture():
    """صفحة التقاط الصورة بالكاميرا"""
    return render_template('camera_capture.html')

@app.route('/upload_photo', methods=['POST'])
@csrf.exempt
@login_required
def upload_photo():
    """رفع صورة الموظف"""
    try:
        if 'photo' not in request.files:
            return {'success': False, 'error': 'لم يتم العثور على صورة'}

        file = request.files['photo']
        if file.filename == '':
            return {'success': False, 'error': 'لم يتم اختيار صورة'}

        if file:
            # إنشاء مجلد الصور إذا لم يكن موجوداً
            import os
            upload_folder = 'static/employee_photos'
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)

            # إنشاء اسم فريد للصورة
            import uuid
            from datetime import datetime
            filename = f"emp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}.jpg"
            filepath = os.path.join(upload_folder, filename)

            # حفظ الصورة
            file.save(filepath)

            # إرجاع مسار الصورة
            photo_url = f"/{filepath}"
            return {
                'success': True,
                'photo_url': photo_url,
                'message': 'تم حفظ الصورة بنجاح'
            }

    except Exception as e:
        return {'success': False, 'error': f'خطأ في حفظ الصورة: {str(e)}'}

    return {'success': False, 'error': 'خطأ غير معروف'}

@app.route('/reports/export/<format>')
@login_required
def export_reports(format):
    """تصدير التقارير"""
    if format == 'excel':
        flash('تصدير Excel قيد التطوير', 'info')
    elif format == 'pdf':
        flash('تصدير PDF قيد التطوير', 'info')
    elif format == 'csv':
        flash('تصدير CSV قيد التطوير', 'info')
    elif format == 'all':
        flash('تصدير جميع التقارير قيد التطوير', 'info')

    return redirect(url_for('reports'))

# ===== الإعدادات =====
@app.route('/settings')
@login_required
def settings():
    """مركز الإعدادات"""
    return render_template('settings_dashboard.html')

@app.route('/settings/work_schedule', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def work_schedule():
    """إعدادات أوقات العمل"""
    if request.method == 'POST':
        try:
            # حفظ إعدادات أوقات العمل
            flash('تم حفظ إعدادات أوقات العمل بنجاح', 'success')
            return redirect(url_for('work_schedule'))
        except Exception as e:
            flash(f'خطأ في حفظ الإعدادات: {str(e)}', 'error')

    return render_template('working_work_schedule.html')

# ===== إدارة الفروع =====
@app.route('/branches')
@login_required
def branches_list():
    """قائمة الفروع"""
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('branches_list.html', branches=branches)

@app.route('/branches/add', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def add_branch():
    """إضافة فرع جديد"""
    if request.method == 'POST':
        try:
            # استلام البيانات
            branch_code = request.form.get('branch_code')
            name = request.form.get('name')
            name_ar = request.form.get('name_ar')
            address = request.form.get('address')
            address_ar = request.form.get('address_ar')
            city = request.form.get('city')
            phone = request.form.get('phone')
            email = request.form.get('email')
            manager_name = request.form.get('manager_name')
            manager_phone = request.form.get('manager_phone')

            # التحقق من عدم تكرار رمز الفرع
            if Branch.query.filter_by(branch_code=branch_code).first():
                flash('رمز الفرع موجود مسبقاً', 'error')
                return redirect(url_for('add_branch'))

            # إنشاء الفرع الجديد
            branch = Branch(
                branch_code=branch_code,
                name=name,
                name_ar=name_ar,
                address=address,
                address_ar=address_ar,
                city=city,
                phone=phone,
                email=email,
                manager_name=manager_name,
                manager_phone=manager_phone
            )

            db.session.add(branch)
            db.session.commit()

            flash('تم إضافة الفرع بنجاح', 'success')
            return redirect(url_for('branches_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة الفرع: {str(e)}', 'error')

    return render_template('add_branch.html')

@app.route('/branches/edit/<int:branch_id>', methods=['GET', 'POST'])
@csrf.exempt
@login_required
def edit_branch(branch_id):
    """تعديل فرع"""
    branch = Branch.query.get_or_404(branch_id)

    if request.method == 'POST':
        try:
            branch.name = request.form.get('name')
            branch.name_ar = request.form.get('name_ar')
            branch.address = request.form.get('address')
            branch.address_ar = request.form.get('address_ar')
            branch.city = request.form.get('city')
            branch.phone = request.form.get('phone')
            branch.email = request.form.get('email')
            branch.manager_name = request.form.get('manager_name')
            branch.manager_phone = request.form.get('manager_phone')
            branch.updated_at = datetime.now()

            db.session.commit()
            flash('تم تحديث بيانات الفرع بنجاح', 'success')
            return redirect(url_for('branches_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث الفرع: {str(e)}', 'error')

    return render_template('edit_branch.html', branch=branch)

@app.route('/branches/view/<int:branch_id>')
@login_required
def view_branch(branch_id):
    """عرض تفاصيل فرع"""
    branch = Branch.query.get_or_404(branch_id)
    employees = Employee.query.filter_by(branch_id=branch_id, status='active').all()
    return render_template('view_branch.html', branch=branch, employees=employees)

@app.route('/branches/delete/<int:branch_id>', methods=['POST'])
@csrf.exempt
@login_required
def delete_branch(branch_id):
    """حذف فرع"""
    try:
        branch = Branch.query.get_or_404(branch_id)

        # التحقق من وجود موظفين في الفرع
        employees_count = Employee.query.filter_by(branch_id=branch_id, status='active').count()
        if employees_count > 0:
            flash(f'لا يمكن حذف الفرع لوجود {employees_count} موظف نشط فيه', 'error')
            return redirect(url_for('branches_list'))

        branch.is_active = False  # حذف منطقي
        branch.updated_at = datetime.now()
        db.session.commit()
        flash('تم حذف الفرع بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الفرع: {str(e)}', 'error')

    return redirect(url_for('branches_list'))

@app.route('/branches/employees/<int:branch_id>')
@login_required
def branch_employees(branch_id):
    """موظفي فرع محدد"""
    branch = Branch.query.get_or_404(branch_id)
    employees = Employee.query.filter_by(branch_id=branch_id, status='active').all()
    return render_template('branch_employees.html', branch=branch, employees=employees)

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        with app.app_context():
            db.drop_all()
            db.create_all()
            
            # إنشاء مستخدم افتراضي
            admin = User(username='admin', role='admin')
            admin.set_password('admin123')
            db.session.add(admin)

            # إنشاء فرع افتراضي
            main_branch = Branch(
                branch_code='BR001',
                name='Main Branch',
                name_ar='الفرع الرئيسي',
                address='123 Main Street',
                address_ar='شارع الرئيسي 123',
                city='Riyadh',
                phone='+966112345678',
                email='<EMAIL>',
                manager_name='Manager Name',
                manager_phone='+966501234567'
            )
            db.session.add(main_branch)
            db.session.flush()  # للحصول على ID الفرع

            # إنشاء موظفين تجريبيين
            employees = [
                Employee(employee_code='EMP001', name='Ahmed Mohammed', name_ar='أحمد محمد', branch_id=main_branch.id),
                Employee(employee_code='EMP002', name='Sara Ali', name_ar='سارة علي', branch_id=main_branch.id),
                Employee(employee_code='EMP003', name='Mohammed Ahmed', name_ar='محمد أحمد', branch_id=main_branch.id)
            ]

            for emp in employees:
                db.session.add(emp)
            
            db.session.commit()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🤖 نظام الحضور الذكي المبسط")
    print("Simple Smart Attendance System")
    print("="*50)
    
    if init_database():
        print("🚀 بدء تشغيل النظام...")
        print("📋 معلومات الدخول:")
        print("- الرابط: http://localhost:9999")
        print("- المستخدم: admin")
        print("- كلمة المرور: admin123")
        print("="*50)
        
        app.run(host='0.0.0.0', port=9999, debug=False, threaded=True)
    else:
        print("❌ فشل في تهيئة النظام")
        input("اضغط Enter للخروج...")
