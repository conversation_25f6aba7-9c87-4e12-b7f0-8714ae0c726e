# Core Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
Flask-Babel==3.1.0

# Database
SQLAlchemy==2.0.21

# Security
Werkzeug==2.3.7
bcrypt==4.0.1
cryptography==41.0.4

# Computer Vision and Face Recognition
opencv-python==4.8.1.78
face-recognition==1.3.0
dlib==19.24.2
numpy==1.24.3
Pillow==10.0.1

# Data Processing and Analysis
pandas==2.0.3
openpyxl==3.1.2
xlsxwriter==3.1.9

# PDF Generation
reportlab==4.0.4
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Web Forms and Validation
WTForms==3.0.1
email-validator==2.0.0

# Date and Time Handling
python-dateutil==2.8.2

# HTTP Requests (for future API integrations)
requests==2.31.0

# Development and Testing
pytest==7.4.2
pytest-flask==1.2.0
coverage==7.3.2

# Internationalization
Babel==2.12.1

# File Handling
python-magic==0.4.27

# Configuration Management
python-dotenv==1.0.0

# Image Processing
scikit-image==0.21.0
matplotlib==3.7.2

# Utilities
click==8.1.7
itsdangerous==2.1.2
MarkupSafe==2.1.3
Jinja2==3.1.2

# For Windows compatibility (camera access)
pywin32==306; sys_platform == "win32"

# Optional: For better performance
gunicorn==21.2.0
gevent==23.7.0
