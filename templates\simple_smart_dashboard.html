<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام الحضور الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stats-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .action-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        
        .btn-action {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-smart {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        
        .btn-smart:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-robot me-2"></i>
                نظام الحضور الذكي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="bi bi-box-arrow-right me-1"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center mb-4">
                    <i class="bi bi-speedometer2 me-2"></i>
                    لوحة التحكم الذكية
                </h2>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="stats-card text-center">
                    <div class="stats-icon text-primary">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stats-number text-primary">{{ employees_count }}</div>
                    <h5>الموظفون المسجلون</h5>
                    <p class="text-muted mb-0">في النظام الذكي</p>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="stats-card text-center">
                    <div class="stats-icon text-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stats-number text-success">{{ today_attendance }}</div>
                    <h5>حضور اليوم</h5>
                    <p class="text-muted mb-0">تسجيلات ذكية</p>
                </div>
            </div>
        </div>
        
        <!-- Main Actions -->
        <div class="row">
            <div class="col-lg-8">
                <div class="action-card pulse">
                    <div class="action-icon">
                        <i class="bi bi-camera-video"></i>
                    </div>
                    <h4 class="mb-3">الحضور الذكي التلقائي</h4>
                    <p class="text-muted mb-4">
                        نظام متطور للتعرف التلقائي على الموظفين وتسجيل الحضور والانصراف
                        بدون تدخل بشري باستخدام تقنيات الذكاء الاصطناعي
                    </p>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-eye text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">كشف تلقائي</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">تسجيل فوري</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-shield-check text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">دقة عالية</h6>
                            </div>
                        </div>
                    </div>
                    
                    <a href="{{ url_for('smart_attendance') }}" class="btn btn-smart btn-action btn-lg">
                        <i class="bi bi-rocket me-2"></i>
                        بدء النظام الذكي
                    </a>
                </div>
            </div>

            <!-- قائمة الأقسام الرئيسية -->
            <div class="col-lg-12 mt-4">
                <div class="row g-3">
                    <!-- إدارة الموظفين -->
                    <div class="col-md-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-people-fill text-primary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">إدارة الموظفين</h6>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="{{ url_for('employees_list') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-list me-1"></i> قائمة الموظفين
                                    </a>
                                    <a href="{{ url_for('add_employee') }}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-plus-circle me-1"></i> إضافة موظف
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إدارة الحضور -->
                    <div class="col-md-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-clock-fill text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">إدارة الحضور</h6>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="{{ url_for('attendance_list') }}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-calendar-check me-1"></i> سجل الحضور
                                    </a>
                                    <a href="{{ url_for('manual_attendance') }}" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-pencil-square me-1"></i> تسجيل يدوي
                                    </a>
                                    <a href="{{ url_for('camera_attendance') }}" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-camera me-1"></i> تسجيل بالكاميرا
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التقارير -->
                    <div class="col-md-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">التقارير</h6>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="{{ url_for('reports') }}" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-graph-up me-1"></i> مركز التقارير
                                    </a>
                                    <a href="{{ url_for('daily_report') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-calendar-day me-1"></i> تقرير يومي
                                    </a>
                                    <a href="{{ url_for('monthly_report') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-calendar-month me-1"></i> تقرير شهري
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إدارة الفروع -->
                    <div class="col-md-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-building-fill text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">إدارة الفروع</h6>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="{{ url_for('branches_list') }}" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-list me-1"></i> قائمة الفروع
                                    </a>
                                    <a href="{{ url_for('add_branch') }}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-plus-circle me-1"></i> إضافة فرع
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات -->
                    <div class="col-md-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-gear-fill text-secondary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">الإعدادات</h6>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-gear me-1"></i> مركز الإعدادات
                                    </a>
                                    <a href="{{ url_for('work_schedule') }}" class="btn btn-outline-dark btn-sm">
                                        <i class="bi bi-clock me-1"></i> أوقات العمل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="action-card">
                            <div class="action-icon text-info">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h5>التقارير</h5>
                            <p class="text-muted">عرض إحصائيات مفصلة</p>
                            <button class="btn btn-outline-info btn-action" disabled>
                                قريباً
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="action-card">
                            <div class="action-icon text-warning">
                                <i class="bi bi-gear"></i>
                            </div>
                            <h5>الإعدادات</h5>
                            <p class="text-muted">تخصيص النظام</p>
                            <button class="btn btn-outline-warning btn-action" disabled>
                                قريباً
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Features Info -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5 class="alert-heading">
                        <i class="bi bi-lightbulb me-2"></i>
                        ميزات النظام الذكي
                    </h5>
                    <hr>
                    <div class="row">
                        <div class="col-md-3">
                            <h6><i class="bi bi-check-circle text-success me-1"></i> كشف الوجوه</h6>
                            <small>تقنية متقدمة لكشف الوجوه في الوقت الفعلي</small>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="bi bi-check-circle text-success me-1"></i> تسجيل تلقائي</h6>
                            <small>تسجيل الحضور والانصراف بدون تدخل</small>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="bi bi-check-circle text-success me-1"></i> مراقبة مستمرة</h6>
                            <small>مراقبة 24/7 مع سجل مفصل</small>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="bi bi-check-circle text-success me-1"></i> واجهة ذكية</h6>
                            <small>تحكم سهل ومعلومات فورية</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير تحميل الصفحة
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
            
            // تحديث الإحصائيات كل 30 ثانية
            setInterval(function() {
                // يمكن إضافة تحديث تلقائي للإحصائيات هنا
                console.log('تحديث الإحصائيات...');
            }, 30000);
        });
    </script>
</body>
</html>
