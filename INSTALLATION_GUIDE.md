# 🚀 Installation and Setup Guide

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux Ubuntu 18.04+
- **Python**: Version 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Camera**: USB webcam or built-in camera (for facial recognition)
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, or Edge 90+

### Recommended Specifications
- **CPU**: Intel i5 or AMD Ryzen 5 (or equivalent)
- **RAM**: 8GB or more
- **Camera**: HD webcam with good low-light performance
- **Network**: Stable internet connection for updates

---

## Step-by-Step Installation

### 1. Python Installation

#### Windows
1. Download Python from [python.org](https://www.python.org/downloads/)
2. Run the installer and **check "Add Python to PATH"**
3. Verify installation:
```cmd
python --version
pip --version
```

#### macOS
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### 2. Download the Project

#### Option A: Git Clone (Recommended)
```bash
git clone https://github.com/your-repo/attendance-system.git
cd attendance-system
```

#### Option B: Download ZIP
1. Download the project ZIP file
2. Extract to your desired location
3. Open terminal/command prompt in the project folder

### 3. Create Virtual Environment

```bash
# Create virtual environment
python -m venv attendance_env

# Activate virtual environment
# Windows:
attendance_env\Scripts\activate

# macOS/Linux:
source attendance_env/bin/activate
```

**Note**: You should see `(attendance_env)` in your terminal prompt when activated.

### 4. Install Dependencies

```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Install all requirements
pip install -r requirements.txt
```

#### Troubleshooting Common Installation Issues

**dlib Installation Issues (Windows)**:
```bash
# If dlib fails to install, try:
pip install cmake
pip install dlib

# Or use pre-compiled wheel:
pip install dlib-19.24.2-cp39-cp39-win_amd64.whl
```

**OpenCV Issues**:
```bash
# If opencv-python fails:
pip install opencv-python-headless
```

**Face Recognition Issues**:
```bash
# Alternative installation:
conda install -c conda-forge dlib
pip install face-recognition
```

### 5. Database Setup

```bash
# Initialize the database
python init_db.py

# Or if init_db.py doesn't exist, create it:
python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Database initialized successfully!')
"
```

### 6. Create Required Directories

```bash
# Create necessary folders
mkdir -p static/uploads/employees
mkdir -p static/uploads/camera_captures
mkdir -p static/images
mkdir -p logs
```

### 7. Configuration Setup

Create a `.env` file in the project root:

```env
# .env file
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///attendance.db
FLASK_ENV=development
FLASK_DEBUG=True

# Camera settings
CAMERA_INDEX=0
FACE_RECOGNITION_TOLERANCE=0.6

# Upload settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=static/uploads

# Language settings
LANGUAGES=en,ar
BABEL_DEFAULT_LOCALE=en
BABEL_DEFAULT_TIMEZONE=UTC
```

### 8. Initialize Admin User

Create an admin user by running:

```python
# create_admin.py
from app import app, db
from models import User
from werkzeug.security import generate_password_hash

with app.app_context():
    admin = User(
        username='admin',
        password_hash=generate_password_hash('admin123'),
        role='admin'
    )
    db.session.add(admin)
    db.session.commit()
    print("Admin user created successfully!")
    print("Username: admin")
    print("Password: admin123")
```

Run it:
```bash
python create_admin.py
```

### 9. Test Camera Access

```python
# test_camera.py
import cv2

def test_camera():
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open camera")
        return False
    
    ret, frame = cap.read()
    if ret:
        print("Camera test successful!")
        cv2.imshow('Camera Test', frame)
        cv2.waitKey(2000)  # Show for 2 seconds
        cv2.destroyAllWindows()
    else:
        print("Error: Could not read from camera")
    
    cap.release()
    return ret

if __name__ == "__main__":
    test_camera()
```

Run the test:
```bash
python test_camera.py
```

### 10. Run the Application

```bash
# Development mode
python app.py

# Or using Flask command
flask run

# For production (using gunicorn)
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 11. Access the Application

1. Open your web browser
2. Navigate to: `http://localhost:5000`
3. Login with:
   - **Username**: admin
   - **Password**: admin123

---

## Post-Installation Configuration

### 1. Add Branches
1. Go to **Settings** → **Branches**
2. Add your organization's branches/departments
3. Set branch managers and contact information

### 2. Add Employees
1. Navigate to **Employees** → **Add New Employee**
2. Fill in employee details
3. **Important**: Take a clear photo for facial recognition
4. Assign to appropriate branch

### 3. Configure Camera Settings
1. Go to **Settings** → **Camera**
2. Test camera functionality
3. Adjust recognition threshold (0.4-0.8 recommended)
4. Set camera resolution and quality

### 4. Language Setup
1. Go to **Settings** → **Language**
2. Choose default language (Arabic/English)
3. Test language switching functionality

### 5. Backup Configuration
1. Set up automatic database backups
2. Configure image backup location
3. Test restore procedures

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Camera Not Working
```bash
# Check camera permissions (Windows)
# Go to Settings → Privacy → Camera → Allow apps to access camera

# Linux camera permissions
sudo usermod -a -G video $USER
# Logout and login again

# Test different camera indices
python -c "
import cv2
for i in range(5):
    cap = cv2.VideoCapture(i)
    if cap.isOpened():
        print(f'Camera {i}: Available')
        cap.release()
    else:
        print(f'Camera {i}: Not available')
"
```

#### 2. Face Recognition Not Working
- Ensure good lighting when taking employee photos
- Use high-quality images (at least 300x300 pixels)
- Retrain recognition model if accuracy is poor
- Adjust recognition tolerance in settings

#### 3. Database Issues
```bash
# Reset database
rm attendance.db
python init_db.py

# Backup database
cp attendance.db attendance_backup.db
```

#### 4. Port Already in Use
```bash
# Find process using port 5000
netstat -ano | findstr :5000  # Windows
lsof -i :5000                 # macOS/Linux

# Kill the process or use different port
python app.py --port 5001
```

#### 5. Permission Errors
```bash
# Windows: Run as administrator
# Linux/macOS: Check file permissions
chmod +x app.py
sudo chown -R $USER:$USER .
```

### Performance Optimization

#### 1. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_emp_id ON attendance(emp_id);
CREATE INDEX idx_employees_branch ON employees(branch_id);
```

#### 2. Image Optimization
- Compress employee photos to reduce storage
- Set maximum image dimensions (e.g., 800x600)
- Use JPEG format for photos

#### 3. Memory Management
- Restart application daily in production
- Monitor memory usage with system tools
- Clear old camera captures periodically

---

## Production Deployment

### Using Gunicorn (Linux/macOS)
```bash
# Install gunicorn
pip install gunicorn

# Run with multiple workers
gunicorn -w 4 -b 0.0.0.0:5000 --timeout 120 app:app

# With logging
gunicorn -w 4 -b 0.0.0.0:5000 --access-logfile access.log --error-logfile error.log app:app
```

### Using Apache/Nginx (Advanced)
- Configure reverse proxy
- Set up SSL certificates
- Enable gzip compression
- Configure static file serving

### Environment Variables for Production
```env
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-very-secure-secret-key
DATABASE_URL=sqlite:///production.db
```

---

## Maintenance

### Regular Tasks
1. **Daily**: Check system logs for errors
2. **Weekly**: Backup database and images
3. **Monthly**: Update dependencies and security patches
4. **Quarterly**: Review and clean old data

### Monitoring
- Set up log rotation
- Monitor disk space usage
- Check camera functionality regularly
- Review attendance accuracy

### Updates
```bash
# Update dependencies
pip install --upgrade -r requirements.txt

# Update application
git pull origin main
# Restart application
```

---

## Support

For technical support:
1. Check this documentation first
2. Review error logs in `logs/` directory
3. Test with minimal configuration
4. Contact development team with detailed error information

**Log Locations**:
- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- Access logs: `logs/access.log`
