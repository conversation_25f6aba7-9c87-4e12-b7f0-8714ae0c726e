# 🚀 Deployment Guide

## Overview

This guide covers deploying the Attendance System in production environments, including server setup, security configurations, and maintenance procedures.

---

## 🏗️ Production Architecture

### Recommended Architecture
```
Internet → Load Balancer → Web Server (Nginx) → Application Server (Gunicorn) → Flask App
                                              ↓
                                         Database (SQLite/PostgreSQL)
                                              ↓
                                         File Storage (Local/Cloud)
```

### System Requirements

#### Minimum Production Requirements
- **CPU**: 2 cores, 2.4GHz
- **RAM**: 4GB
- **Storage**: 50GB SSD
- **OS**: Ubuntu 20.04 LTS or CentOS 8
- **Network**: 100Mbps connection

#### Recommended Production Requirements
- **CPU**: 4 cores, 3.0GHz
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Network**: 1Gbps connection
- **Backup**: Separate storage for backups

---

## 🐧 Linux Server Setup

### 1. Initial Server Configuration

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y python3 python3-pip python3-venv nginx supervisor git

# Create application user
sudo useradd -m -s /bin/bash attendance
sudo usermod -aG sudo attendance

# Create application directory
sudo mkdir -p /opt/attendance-system
sudo chown attendance:attendance /opt/attendance-system
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - attendance

# Clone application
cd /opt/attendance-system
git clone https://github.com/your-repo/attendance-system.git .

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn

# Set up environment variables
cp .env.example .env
nano .env  # Edit configuration
```

### 3. Environment Configuration

Create `/opt/attendance-system/.env`:
```env
# Production Environment
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-very-secure-secret-key-here

# Database
DATABASE_URL=sqlite:////opt/attendance-system/data/attendance.db

# File uploads
UPLOAD_FOLDER=/opt/attendance-system/uploads
MAX_CONTENT_LENGTH=16777216

# Camera settings
CAMERA_INDEX=0
FACE_RECOGNITION_TOLERANCE=0.6

# Security
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
PERMANENT_SESSION_LIFETIME=3600

# Logging
LOG_LEVEL=INFO
LOG_FILE=/opt/attendance-system/logs/app.log
```

### 4. Database Setup

```bash
# Create data directory
mkdir -p /opt/attendance-system/data
mkdir -p /opt/attendance-system/logs
mkdir -p /opt/attendance-system/uploads

# Initialize database
python init_db.py

# Create admin user
python create_admin.py
```

---

## 🌐 Web Server Configuration

### Nginx Configuration

Create `/etc/nginx/sites-available/attendance-system`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # File upload size
    client_max_body_size 20M;
    
    # Static files
    location /static {
        alias /opt/attendance-system/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads {
        alias /opt/attendance-system/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # WebSocket support (for real-time features)
    location /socket.io {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/attendance-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔒 SSL Certificate Setup

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run

# Set up auto-renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

---

## 🔧 Application Server Configuration

### Gunicorn Configuration

Create `/opt/attendance-system/gunicorn.conf.py`:
```python
# Gunicorn configuration
bind = "127.0.0.1:8000"
workers = 4
worker_class = "gevent"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2

# Logging
accesslog = "/opt/attendance-system/logs/gunicorn_access.log"
errorlog = "/opt/attendance-system/logs/gunicorn_error.log"
loglevel = "info"

# Process naming
proc_name = "attendance-system"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Performance
preload_app = True
```

### Supervisor Configuration

Create `/etc/supervisor/conf.d/attendance-system.conf`:
```ini
[program:attendance-system]
command=/opt/attendance-system/venv/bin/gunicorn -c gunicorn.conf.py app:app
directory=/opt/attendance-system
user=attendance
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/attendance-system/logs/supervisor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/opt/attendance-system/venv/bin"
```

Start the service:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start attendance-system
sudo supervisorctl status
```

---

## 🗄️ Database Configuration

### SQLite (Default)
```bash
# Set proper permissions
chmod 664 /opt/attendance-system/data/attendance.db
chown attendance:attendance /opt/attendance-system/data/attendance.db
```

### PostgreSQL (Recommended for Production)

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE attendance_system;
CREATE USER attendance_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE attendance_system TO attendance_user;
\q

# Update environment variables
DATABASE_URL=postgresql://attendance_user:secure_password@localhost/attendance_system
```

Install PostgreSQL adapter:
```bash
pip install psycopg2-binary
```

---

## 📊 Monitoring and Logging

### Log Configuration

Create `/opt/attendance-system/logging.conf`:
```ini
[loggers]
keys=root,gunicorn.error,gunicorn.access

[handlers]
keys=console,error_file,access_file

[formatters]
keys=generic,access

[logger_root]
level=INFO
handlers=console

[logger_gunicorn.error]
level=INFO
handlers=error_file
propagate=1
qualname=gunicorn.error

[logger_gunicorn.access]
level=INFO
handlers=access_file
propagate=0
qualname=gunicorn.access

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout, )

[handler_error_file]
class=logging.handlers.RotatingFileHandler
formatter=generic
args=('/opt/attendance-system/logs/error.log', 'a', 10485760, 5)

[handler_access_file]
class=logging.handlers.RotatingFileHandler
formatter=access
args=('/opt/attendance-system/logs/access.log', 'a', 10485760, 5)

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter

[formatter_access]
format=%(message)s
class=logging.Formatter
```

### System Monitoring

Install monitoring tools:
```bash
# Install htop for system monitoring
sudo apt install htop

# Install logwatch for log analysis
sudo apt install logwatch

# Configure logwatch
sudo nano /etc/logwatch/conf/logwatch.conf
```

---

## 🔄 Backup and Recovery

### Automated Backup Script

Create `/opt/attendance-system/scripts/backup.sh`:
```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/backups/attendance-system"
APP_DIR="/opt/attendance-system"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
if [ -f "$APP_DIR/data/attendance.db" ]; then
    sqlite3 $APP_DIR/data/attendance.db ".backup $BACKUP_DIR/db_$DATE.db"
fi

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C $APP_DIR uploads static/uploads

# Configuration backup
cp $APP_DIR/.env $BACKUP_DIR/env_$DATE.backup

# Clean old backups
find $BACKUP_DIR -name "*.db" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.backup" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $DATE"
```

Make executable and schedule:
```bash
chmod +x /opt/attendance-system/scripts/backup.sh

# Add to crontab
echo "0 2 * * * /opt/attendance-system/scripts/backup.sh" | crontab -
```

### Recovery Procedures

```bash
# Restore database
sqlite3 /opt/attendance-system/data/attendance.db ".restore /opt/backups/attendance-system/db_YYYYMMDD_HHMMSS.db"

# Restore files
tar -xzf /opt/backups/attendance-system/files_YYYYMMDD_HHMMSS.tar.gz -C /opt/attendance-system/

# Restart services
sudo supervisorctl restart attendance-system
```

---

## 🔧 Maintenance

### Regular Maintenance Tasks

#### Daily
- Check application logs for errors
- Monitor system resources (CPU, memory, disk)
- Verify backup completion

#### Weekly
- Review attendance data accuracy
- Check camera system functionality
- Update system packages

#### Monthly
- Analyze performance metrics
- Review security logs
- Test backup restoration
- Update application dependencies

### Performance Optimization

```bash
# Database optimization
sqlite3 /opt/attendance-system/data/attendance.db "VACUUM;"
sqlite3 /opt/attendance-system/data/attendance.db "ANALYZE;"

# Log rotation
sudo logrotate -f /etc/logrotate.d/attendance-system

# Clear temporary files
find /tmp -name "*.tmp" -mtime +7 -delete
```

### Security Updates

```bash
# System updates
sudo apt update && sudo apt upgrade -y

# Python package updates
source /opt/attendance-system/venv/bin/activate
pip list --outdated
pip install --upgrade package_name

# Restart services after updates
sudo supervisorctl restart attendance-system
sudo systemctl reload nginx
```

---

## 🚨 Troubleshooting

### Common Production Issues

#### High Memory Usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Restart application if needed
sudo supervisorctl restart attendance-system
```

#### Database Lock Issues
```bash
# Check for long-running queries
sqlite3 /opt/attendance-system/data/attendance.db ".timeout 30000"

# Restart application
sudo supervisorctl restart attendance-system
```

#### Camera Access Problems
```bash
# Check camera permissions
ls -l /dev/video*
sudo usermod -a -G video attendance

# Test camera access
python3 -c "import cv2; cap = cv2.VideoCapture(0); print('Camera OK' if cap.isOpened() else 'Camera Error')"
```

### Log Analysis

```bash
# Check application logs
tail -f /opt/attendance-system/logs/app.log

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log

# Check system logs
sudo journalctl -u supervisor -f
```

---

## 📞 Support and Maintenance Contacts

### Emergency Contacts
- **System Administrator**: [contact info]
- **Database Administrator**: [contact info]
- **Network Administrator**: [contact info]

### Escalation Procedures
1. **Level 1**: Application restart
2. **Level 2**: System administrator intervention
3. **Level 3**: Vendor support contact

---

*This deployment guide should be customized based on your specific infrastructure and requirements.*
