# 🧪 دليل الاختبار - Testing Guide

## نظرة عامة - Overview

يحتوي هذا الدليل على إرشادات شاملة لاختبار نظام الحضور والانصراف، بما في ذلك الاختبارات الوحدة والتكامل والأداء.

This guide contains comprehensive instructions for testing the attendance system, including unit, integration, and performance tests.

---

## 🏗️ إعداد بيئة الاختبار - Test Environment Setup

### متطلبات الاختبار - Testing Requirements

```bash
# تثبيت أدوات الاختبار - Install testing tools
pip install pytest pytest-flask pytest-cov selenium webdriver-manager
pip install factory-boy faker coverage pytest-mock
pip install locust  # لاختبار الأداء - For performance testing
```

### إعداد قاعدة بيانات الاختبار - Test Database Setup

```python
# config/test_config.py
import os
import tempfile

class TestConfig:
    TESTING = True
    WTF_CSRF_ENABLED = False

    # قاعدة بيانات مؤقتة للاختبار - Temporary test database
    db_fd, DATABASE_PATH = tempfile.mkstemp()
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}'

    # إعدادات الكاميرا للاختبار - Camera settings for testing
    CAMERA_ENABLED = False
    FACE_RECOGNITION_ENABLED = False

    # مجلدات مؤقتة - Temporary folders
    UPLOAD_FOLDER = tempfile.mkdtemp()

    @staticmethod
    def cleanup():
        """تنظيف ملفات الاختبار - Cleanup test files"""
        os.close(TestConfig.db_fd)
        os.unlink(TestConfig.DATABASE_PATH)
```

---

## 🔬 الاختبارات الوحدة - Unit Tests

### اختبار نماذج البيانات - Model Tests

```python
# tests/test_models.py
import pytest
from datetime import datetime, date
from app import create_app, db
from models import User, Employee, Branch, Attendance

class TestUserModel:
    """اختبارات نموذج المستخدم - User model tests"""

    def test_password_hashing(self, app):
        """اختبار تشفير كلمة المرور - Test password hashing"""
        user = User(username='testuser')
        user.set_password('testpassword')

        assert user.password_hash != 'testpassword'
        assert user.check_password('testpassword') == True
        assert user.check_password('wrongpassword') == False

    def test_user_creation(self, app):
        """اختبار إنشاء مستخدم - Test user creation"""
        user = User(
            username='testuser',
            email='<EMAIL>',
            role='admin'
        )
        user.set_password('password123')

        db.session.add(user)
        db.session.commit()

        saved_user = User.query.filter_by(username='testuser').first()
        assert saved_user is not None
        assert saved_user.email == '<EMAIL>'
        assert saved_user.role == 'admin'

class TestEmployeeModel:
    """اختبارات نموذج الموظف - Employee model tests"""

    def test_employee_creation(self, app, sample_branch):
        """اختبار إنشاء موظف - Test employee creation"""
        employee = Employee(
            employee_code='EMP001',
            name='أحمد محمد',
            name_ar='أحمد محمد',
            job_title='مطور برمجيات',
            branch_id=sample_branch.id,
            hire_date=date.today()
        )

        db.session.add(employee)
        db.session.commit()

        saved_employee = Employee.query.filter_by(employee_code='EMP001').first()
        assert saved_employee is not None
        assert saved_employee.name == 'أحمد محمد'
        assert saved_employee.branch_id == sample_branch.id

    def test_employee_full_name(self, app):
        """اختبار الاسم الكامل - Test full name"""
        employee = Employee(
            name='John Doe',
            name_ar='جون دو'
        )

        assert employee.get_display_name('en') == 'John Doe'
        assert employee.get_display_name('ar') == 'جون دو'

class TestAttendanceModel:
    """اختبارات نموذج الحضور - Attendance model tests"""

    def test_attendance_creation(self, app, sample_employee):
        """اختبار إنشاء سجل حضور - Test attendance creation"""
        attendance = Attendance(
            emp_id=sample_employee.id,
            date=date.today(),
            time_in=datetime.now().time(),
            status='present',
            attendance_method='manual'
        )

        db.session.add(attendance)
        db.session.commit()

        saved_attendance = Attendance.query.filter_by(emp_id=sample_employee.id).first()
        assert saved_attendance is not None
        assert saved_attendance.status == 'present'

    def test_work_hours_calculation(self, app, sample_employee):
        """اختبار حساب ساعات العمل - Test work hours calculation"""
        from datetime import time

        attendance = Attendance(
            emp_id=sample_employee.id,
            date=date.today(),
            time_in=time(8, 0),  # 8:00 AM
            time_out=time(17, 0),  # 5:00 PM
            total_break_minutes=60  # 1 hour break
        )

        work_hours = attendance.calculate_work_hours()
        assert work_hours == 8.0  # 9 hours - 1 hour break = 8 hours
```

### اختبار الوظائف المساعدة - Utility Function Tests

```python
# tests/test_utils.py
import pytest
from datetime import datetime, date
from utils.date_helpers import is_working_day, calculate_late_minutes
from utils.face_recognition import encode_face, compare_faces
from utils.report_generator import generate_attendance_report

class TestDateHelpers:
    """اختبار مساعدات التاريخ - Date helper tests"""

    def test_is_working_day(self):
        """اختبار تحديد أيام العمل - Test working day detection"""
        # الاثنين - Monday
        monday = date(2024, 1, 15)
        assert is_working_day(monday) == True

        # الجمعة - Friday
        friday = date(2024, 1, 19)
        assert is_working_day(friday) == False

        # السبت - Saturday
        saturday = date(2024, 1, 20)
        assert is_working_day(saturday) == False

    def test_calculate_late_minutes(self):
        """اختبار حساب دقائق التأخير - Test late minutes calculation"""
        from datetime import time

        # وصول في الوقت - On time arrival
        on_time = calculate_late_minutes(time(8, 0), time(8, 0))
        assert on_time == 0

        # تأخير 15 دقيقة - 15 minutes late
        late = calculate_late_minutes(time(8, 15), time(8, 0))
        assert late == 15

        # وصول مبكر - Early arrival
        early = calculate_late_minutes(time(7, 45), time(8, 0))
        assert early == 0

class TestFaceRecognition:
    """اختبار التعرف على الوجه - Face recognition tests"""

    @pytest.mark.skipif(not os.path.exists('test_images/'),
                       reason="Test images not available")
    def test_face_encoding(self):
        """اختبار ترميز الوجه - Test face encoding"""
        image_path = 'test_images/sample_face.jpg'
        encoding = encode_face(image_path)

        assert encoding is not None
        assert len(encoding) == 128  # Face encoding should be 128 dimensions

    def test_face_comparison(self):
        """اختبار مقارنة الوجوه - Test face comparison"""
        # استخدام ترميزات وهمية للاختبار - Use dummy encodings for testing
        encoding1 = [0.1] * 128
        encoding2 = [0.1] * 128  # نفس الترميز - Same encoding
        encoding3 = [0.9] * 128  # ترميز مختلف - Different encoding

        # نفس الوجه - Same face
        similarity1 = compare_faces([encoding1], encoding2, tolerance=0.6)
        assert len(similarity1) > 0

        # وجه مختلف - Different face
        similarity2 = compare_faces([encoding1], encoding3, tolerance=0.6)
        assert len(similarity2) == 0
```

---

## 🔗 اختبارات التكامل - Integration Tests

### اختبار واجهات برمجة التطبيقات - API Tests

```python
# tests/test_api.py
import pytest
import json
from flask import url_for

class TestAuthAPI:
    """اختبار API المصادقة - Authentication API tests"""

    def test_login_success(self, client, sample_user):
        """اختبار تسجيل دخول ناجح - Test successful login"""
        response = client.post('/api/v1/auth/login',
                             json={
                                 'username': sample_user.username,
                                 'password': 'testpassword'
                             })

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'token' in data

    def test_login_failure(self, client):
        """اختبار فشل تسجيل الدخول - Test login failure"""
        response = client.post('/api/v1/auth/login',
                             json={
                                 'username': 'nonexistent',
                                 'password': 'wrongpassword'
                             })

        assert response.status_code == 401
        data = json.loads(response.data)
        assert data['success'] == False

class TestEmployeeAPI:
    """اختبار API الموظفين - Employee API tests"""

    def test_get_employees(self, client, auth_headers, sample_employees):
        """اختبار جلب قائمة الموظفين - Test get employees list"""
        response = client.get('/api/v1/employees', headers=auth_headers)

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert len(data['data']) >= 1

    def test_create_employee(self, client, auth_headers, sample_branch):
        """اختبار إنشاء موظف جديد - Test create new employee"""
        employee_data = {
            'employee_code': 'EMP999',
            'name': 'Test Employee',
            'name_ar': 'موظف تجريبي',
            'job_title': 'Tester',
            'branch_id': sample_branch.id,
            'hire_date': '2024-01-01'
        }

        response = client.post('/api/v1/employees',
                             json=employee_data,
                             headers=auth_headers)

        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] == True
        assert data['data']['employee_code'] == 'EMP999'

class TestAttendanceAPI:
    """اختبار API الحضور - Attendance API tests"""

    def test_record_attendance(self, client, auth_headers, sample_employee):
        """اختبار تسجيل الحضور - Test record attendance"""
        attendance_data = {
            'emp_id': sample_employee.id,
            'action': 'check_in',
            'method': 'manual'
        }

        response = client.post('/api/v1/attendance/record',
                             json=attendance_data,
                             headers=auth_headers)

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True

    def test_get_attendance_today(self, client, auth_headers):
        """اختبار جلب حضور اليوم - Test get today's attendance"""
        response = client.get('/api/v1/attendance/today', headers=auth_headers)

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'data' in data
```

---

## 🌐 اختبارات واجهة المستخدم - UI Tests

### اختبار Selenium

```python
# tests/test_ui.py
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class TestLoginUI:
    """اختبار واجهة تسجيل الدخول - Login UI tests"""

    @pytest.fixture
    def driver(self):
        """إعداد متصفح الاختبار - Setup test browser"""
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')  # تشغيل بدون واجهة - Run headless
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')

        driver = webdriver.Chrome(
            ChromeDriverManager().install(),
            options=options
        )
        yield driver
        driver.quit()

    def test_login_page_loads(self, driver, live_server):
        """اختبار تحميل صفحة تسجيل الدخول - Test login page loads"""
        driver.get(f"{live_server.url}/login")

        assert "تسجيل الدخول" in driver.title or "Login" in driver.title

        username_field = driver.find_element(By.NAME, "username")
        password_field = driver.find_element(By.NAME, "password")
        login_button = driver.find_element(By.TYPE, "submit")

        assert username_field.is_displayed()
        assert password_field.is_displayed()
        assert login_button.is_displayed()

    def test_successful_login(self, driver, live_server, sample_user):
        """اختبار تسجيل دخول ناجح - Test successful login"""
        driver.get(f"{live_server.url}/login")

        username_field = driver.find_element(By.NAME, "username")
        password_field = driver.find_element(By.NAME, "password")
        login_button = driver.find_element(By.TYPE, "submit")

        username_field.send_keys(sample_user.username)
        password_field.send_keys('testpassword')
        login_button.click()

        # انتظار التوجيه للوحة التحكم - Wait for redirect to dashboard
        WebDriverWait(driver, 10).until(
            EC.url_contains("/dashboard")
        )

        assert "/dashboard" in driver.current_url

class TestEmployeeManagementUI:
    """اختبار واجهة إدارة الموظفين - Employee management UI tests"""

    def test_add_employee_form(self, driver, live_server, logged_in_user):
        """اختبار نموذج إضافة موظف - Test add employee form"""
        driver.get(f"{live_server.url}/employees/add")

        # ملء النموذج - Fill the form
        name_field = driver.find_element(By.NAME, "name")
        name_field.send_keys("موظف تجريبي")

        job_title_field = driver.find_element(By.NAME, "job_title")
        job_title_field.send_keys("مطور")

        # اختيار الفرع - Select branch
        branch_select = driver.find_element(By.NAME, "branch_id")
        branch_select.click()

        # إرسال النموذج - Submit form
        submit_button = driver.find_element(By.TYPE, "submit")
        submit_button.click()

        # التحقق من نجاح الإضافة - Verify successful addition
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "alert-success"))
        )
```

---

## ⚡ اختبارات الأداء - Performance Tests

### اختبار الحمولة باستخدام Locust

```python
# tests/performance/locustfile.py
from locust import HttpUser, task, between
import random

class AttendanceSystemUser(HttpUser):
    """محاكي مستخدم نظام الحضور - Attendance system user simulator"""

    wait_time = between(1, 3)  # انتظار بين 1-3 ثواني - Wait 1-3 seconds

    def on_start(self):
        """تسجيل الدخول عند البدء - Login on start"""
        self.login()

    def login(self):
        """تسجيل الدخول - Login"""
        response = self.client.post("/api/v1/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })

        if response.status_code == 200:
            data = response.json()
            self.token = data.get('token')
            self.headers = {'Authorization': f'Bearer {self.token}'}
        else:
            self.headers = {}

    @task(3)
    def view_dashboard(self):
        """عرض لوحة التحكم - View dashboard"""
        self.client.get("/dashboard", headers=self.headers)

    @task(2)
    def view_employees(self):
        """عرض قائمة الموظفين - View employees list"""
        self.client.get("/api/v1/employees", headers=self.headers)

    @task(1)
    def record_attendance(self):
        """تسجيل حضور - Record attendance"""
        emp_id = random.randint(1, 10)  # افتراض وجود 10 موظفين - Assume 10 employees
        action = random.choice(['check_in', 'check_out'])

        self.client.post("/api/v1/attendance/record",
                        json={
                            'emp_id': emp_id,
                            'action': action,
                            'method': 'manual'
                        },
                        headers=self.headers)

    @task(1)
    def generate_report(self):
        """إنشاء تقرير - Generate report"""
        self.client.post("/api/v1/reports/attendance",
                        json={
                            'report_type': 'daily',
                            'date_from': '2024-01-01',
                            'date_to': '2024-01-31',
                            'format': 'json'
                        },
                        headers=self.headers)

# تشغيل اختبار الأداء - Run performance test
# locust -f tests/performance/locustfile.py --host=http://localhost:5000
```

### اختبار أداء قاعدة البيانات

```python
# tests/test_database_performance.py
import pytest
import time
from datetime import date, datetime, timedelta
from models import Employee, Attendance, db

class TestDatabasePerformance:
    """اختبار أداء قاعدة البيانات - Database performance tests"""

    def test_bulk_employee_creation(self, app):
        """اختبار إنشاء موظفين بالجملة - Test bulk employee creation"""
        start_time = time.time()

        employees = []
        for i in range(1000):
            employee = Employee(
                employee_code=f'EMP{i:04d}',
                name=f'Employee {i}',
                job_title='Test Employee',
                branch_id=1,
                hire_date=date.today()
            )
            employees.append(employee)

        db.session.bulk_save_objects(employees)
        db.session.commit()

        end_time = time.time()
        execution_time = end_time - start_time

        # يجب أن يكتمل في أقل من 5 ثواني - Should complete in less than 5 seconds
        assert execution_time < 5.0

        # التحقق من إنشاء جميع الموظفين - Verify all employees created
        count = Employee.query.count()
        assert count >= 1000

    def test_attendance_query_performance(self, app, sample_employees):
        """اختبار أداء استعلامات الحضور - Test attendance query performance"""
        # إنشاء بيانات حضور كثيرة - Create lots of attendance data
        attendance_records = []
        for i in range(10000):
            emp_id = (i % len(sample_employees)) + 1
            record_date = date.today() - timedelta(days=i % 365)

            attendance = Attendance(
                emp_id=emp_id,
                date=record_date,
                time_in=datetime.now().time(),
                status='present'
            )
            attendance_records.append(attendance)

        db.session.bulk_save_objects(attendance_records)
        db.session.commit()

        # اختبار استعلام معقد - Test complex query
        start_time = time.time()

        result = db.session.query(Attendance).join(Employee).filter(
            Attendance.date >= date.today() - timedelta(days=30),
            Employee.status == 'active'
        ).all()

        end_time = time.time()
        execution_time = end_time - start_time

        # يجب أن يكتمل في أقل من 2 ثانية - Should complete in less than 2 seconds
        assert execution_time < 2.0
        assert len(result) > 0

---

## 📷 اختبار الكاميرا والتعرف على الوجه - Camera & Face Recognition Tests

### اختبار وحدة الكاميرا - Camera Module Tests

```python
# tests/test_camera.py
import pytest
import cv2
import numpy as np
from unittest.mock import Mock, patch
from camera.camera_manager import CameraManager
from camera.face_detector import FaceDetector

class TestCameraManager:
    """اختبار مدير الكاميرا - Camera manager tests"""

    @patch('cv2.VideoCapture')
    def test_camera_initialization(self, mock_video_capture):
        """اختبار تهيئة الكاميرا - Test camera initialization"""
        mock_cap = Mock()
        mock_cap.isOpened.return_value = True
        mock_video_capture.return_value = mock_cap

        camera_manager = CameraManager(camera_index=0)
        result = camera_manager.initialize()

        assert result == True
        mock_video_capture.assert_called_once_with(0)

    @patch('cv2.VideoCapture')
    def test_camera_capture(self, mock_video_capture):
        """اختبار التقاط الصورة - Test image capture"""
        # إنشاء صورة وهمية - Create dummy image
        dummy_image = np.zeros((480, 640, 3), dtype=np.uint8)

        mock_cap = Mock()
        mock_cap.isOpened.return_value = True
        mock_cap.read.return_value = (True, dummy_image)
        mock_video_capture.return_value = mock_cap

        camera_manager = CameraManager(camera_index=0)
        camera_manager.initialize()

        success, image = camera_manager.capture_frame()

        assert success == True
        assert image is not None
        assert image.shape == (480, 640, 3)

class TestFaceDetector:
    """اختبار كاشف الوجوه - Face detector tests"""

    def test_face_detection_with_mock_image(self):
        """اختبار كشف الوجه مع صورة وهمية - Test face detection with mock image"""
        # إنشاء صورة وهمية بوجه - Create dummy image with face
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        # رسم مستطيل يمثل وجه - Draw rectangle representing face
        cv2.rectangle(image, (200, 150), (400, 350), (255, 255, 255), -1)

        detector = FaceDetector()
        faces = detector.detect_faces(image)

        # يجب أن يكتشف على الأقل منطقة واحدة - Should detect at least one region
        assert len(faces) >= 0  # قد لا يكتشف الوجه الوهمي - May not detect dummy face

    @pytest.mark.skipif(not os.path.exists('test_images/face_sample.jpg'),
                       reason="Test face image not available")
    def test_face_detection_with_real_image(self):
        """اختبار كشف الوجه مع صورة حقيقية - Test face detection with real image"""
        image = cv2.imread('test_images/face_sample.jpg')

        detector = FaceDetector()
        faces = detector.detect_faces(image)

        assert len(faces) > 0

        # التحقق من إحداثيات الوجه - Verify face coordinates
        for (x, y, w, h) in faces:
            assert x >= 0 and y >= 0
            assert w > 0 and h > 0
            assert x + w <= image.shape[1]
            assert y + h <= image.shape[0]
```

### اختبار التعرف على الوجه - Face Recognition Tests

```python
# tests/test_face_recognition.py
import pytest
import numpy as np
from unittest.mock import Mock, patch
from face_recognition_module import FaceRecognizer, FaceEncoder

class TestFaceRecognizer:
    """اختبار التعرف على الوجه - Face recognizer tests"""

    def test_face_encoding_generation(self):
        """اختبار إنشاء ترميز الوجه - Test face encoding generation"""
        # ترميز وهمي للاختبار - Dummy encoding for testing
        dummy_encoding = np.random.rand(128)

        with patch('face_recognition.face_encodings') as mock_encodings:
            mock_encodings.return_value = [dummy_encoding]

            recognizer = FaceRecognizer()
            encoding = recognizer.encode_face('dummy_image_path')

            assert encoding is not None
            assert len(encoding) == 128

    def test_face_comparison(self):
        """اختبار مقارنة الوجوه - Test face comparison"""
        # ترميزات وهمية - Dummy encodings
        known_encoding = np.random.rand(128)
        test_encoding1 = known_encoding + np.random.rand(128) * 0.1  # مشابه - Similar
        test_encoding2 = np.random.rand(128)  # مختلف - Different

        recognizer = FaceRecognizer(tolerance=0.6)

        # اختبار التطابق - Test match
        match1 = recognizer.compare_faces([known_encoding], test_encoding1)
        assert len(match1) > 0

        # اختبار عدم التطابق - Test no match
        match2 = recognizer.compare_faces([known_encoding], test_encoding2)
        assert len(match2) == 0

    def test_employee_recognition(self, sample_employees):
        """اختبار التعرف على الموظف - Test employee recognition"""
        recognizer = FaceRecognizer()

        # إضافة ترميزات الموظفين - Add employee encodings
        for employee in sample_employees:
            if employee.face_encoding:
                encoding = np.fromstring(employee.face_encoding, sep=',')
                recognizer.add_known_face(employee.id, encoding, employee.name)

        # اختبار التعرف - Test recognition
        test_encoding = np.random.rand(128)
        result = recognizer.recognize_face(test_encoding)

        # قد يعيد نتيجة أو None - May return result or None
        assert result is None or isinstance(result, dict)
```

---

## 📊 اختبار التقارير - Report Testing

### اختبار إنشاء التقارير - Report Generation Tests

```python
# tests/test_reports.py
import pytest
import os
import tempfile
from datetime import date, timedelta
from reports.report_generator import ReportGenerator
from reports.pdf_generator import PDFGenerator
from reports.excel_generator import ExcelGenerator

class TestReportGenerator:
    """اختبار مولد التقارير - Report generator tests"""

    def test_daily_report_generation(self, app, sample_attendance_data):
        """اختبار إنشاء تقرير يومي - Test daily report generation"""
        generator = ReportGenerator()

        report_data = generator.generate_daily_report(
            date=date.today(),
            branch_id=None
        )

        assert 'summary' in report_data
        assert 'employees' in report_data
        assert 'total_employees' in report_data['summary']
        assert 'present_count' in report_data['summary']
        assert 'absent_count' in report_data['summary']

    def test_monthly_report_generation(self, app, sample_attendance_data):
        """اختبار إنشاء تقرير شهري - Test monthly report generation"""
        generator = ReportGenerator()

        start_date = date.today().replace(day=1)
        end_date = date.today()

        report_data = generator.generate_monthly_report(
            start_date=start_date,
            end_date=end_date,
            branch_id=None
        )

        assert 'period' in report_data
        assert 'statistics' in report_data
        assert 'employees' in report_data

    def test_employee_specific_report(self, app, sample_employee, sample_attendance_data):
        """اختبار تقرير خاص بموظف - Test employee-specific report"""
        generator = ReportGenerator()

        report_data = generator.generate_employee_report(
            emp_id=sample_employee.id,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )

        assert 'employee_info' in report_data
        assert 'attendance_records' in report_data
        assert 'statistics' in report_data

class TestPDFGenerator:
    """اختبار مولد PDF - PDF generator tests"""

    def test_pdf_creation(self, sample_report_data):
        """اختبار إنشاء ملف PDF - Test PDF file creation"""
        generator = PDFGenerator()

        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            pdf_path = tmp_file.name

        try:
            generator.generate_attendance_report(
                data=sample_report_data,
                output_path=pdf_path,
                language='ar'
            )

            assert os.path.exists(pdf_path)
            assert os.path.getsize(pdf_path) > 0

        finally:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)

    def test_arabic_text_rendering(self, sample_report_data):
        """اختبار عرض النص العربي - Test Arabic text rendering"""
        generator = PDFGenerator()

        # اختبار النص العربي - Test Arabic text
        arabic_text = "تقرير الحضور والانصراف"
        processed_text = generator.process_arabic_text(arabic_text)

        assert processed_text is not None
        assert len(processed_text) > 0

class TestExcelGenerator:
    """اختبار مولد Excel - Excel generator tests"""

    def test_excel_creation(self, sample_report_data):
        """اختبار إنشاء ملف Excel - Test Excel file creation"""
        generator = ExcelGenerator()

        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            excel_path = tmp_file.name

        try:
            generator.generate_attendance_report(
                data=sample_report_data,
                output_path=excel_path
            )

            assert os.path.exists(excel_path)
            assert os.path.getsize(excel_path) > 0

        finally:
            if os.path.exists(excel_path):
                os.unlink(excel_path)

    def test_multiple_sheets_creation(self, sample_report_data):
        """اختبار إنشاء أوراق متعددة - Test multiple sheets creation"""
        generator = ExcelGenerator()

        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            excel_path = tmp_file.name

        try:
            generator.generate_detailed_report(
                data=sample_report_data,
                output_path=excel_path,
                include_charts=True
            )

            # فتح الملف للتحقق من الأوراق - Open file to check sheets
            import openpyxl
            workbook = openpyxl.load_workbook(excel_path)

            assert len(workbook.sheetnames) >= 2
            assert 'Summary' in workbook.sheetnames
            assert 'Details' in workbook.sheetnames

        finally:
            if os.path.exists(excel_path):
                os.unlink(excel_path)
```

---

## 🔒 اختبار الأمان - Security Tests

### اختبار المصادقة والتخويل - Authentication & Authorization Tests

```python
# tests/test_security.py
import pytest
from flask import url_for
from models import User
from utils.security import SecurityManager

class TestAuthentication:
    """اختبار المصادقة - Authentication tests"""

    def test_password_strength_validation(self):
        """اختبار التحقق من قوة كلمة المرور - Test password strength validation"""
        security = SecurityManager()

        # كلمة مرور ضعيفة - Weak password
        weak_passwords = ['123', 'password', 'abc123']
        for password in weak_passwords:
            is_valid, message = security.validate_password_strength(password)
            assert is_valid == False

        # كلمة مرور قوية - Strong password
        strong_password = 'StrongP@ssw0rd123'
        is_valid, message = security.validate_password_strength(strong_password)
        assert is_valid == True

    def test_login_attempt_limiting(self, client):
        """اختبار تحديد محاولات تسجيل الدخول - Test login attempt limiting"""
        # محاولات دخول فاشلة متعددة - Multiple failed login attempts
        for i in range(6):  # أكثر من الحد المسموح - More than allowed limit
            response = client.post('/login', data={
                'username': 'testuser',
                'password': 'wrongpassword'
            })

        # المحاولة التالية يجب أن تكون محظورة - Next attempt should be blocked
        response = client.post('/login', data={
            'username': 'testuser',
            'password': 'correctpassword'
        })

        assert response.status_code == 429  # Too Many Requests

    def test_session_timeout(self, client, sample_user):
        """اختبار انتهاء صلاحية الجلسة - Test session timeout"""
        # تسجيل دخول - Login
        client.post('/login', data={
            'username': sample_user.username,
            'password': 'testpassword'
        })

        # محاكاة انتهاء الجلسة - Simulate session expiry
        with client.session_transaction() as sess:
            sess['_permanent'] = False
            sess.permanent = False

        # محاولة الوصول لصفحة محمية - Try to access protected page
        response = client.get('/dashboard')
        assert response.status_code == 302  # Redirect to login

class TestAuthorization:
    """اختبار التخويل - Authorization tests"""

    def test_admin_only_access(self, client, sample_admin_user, sample_regular_user):
        """اختبار الوصول للمديرين فقط - Test admin-only access"""
        # تسجيل دخول مستخدم عادي - Login as regular user
        client.post('/login', data={
            'username': sample_regular_user.username,
            'password': 'testpassword'
        })

        # محاولة الوصول لصفحة المديرين - Try to access admin page
        response = client.get('/admin/settings')
        assert response.status_code == 403  # Forbidden

        # تسجيل خروج - Logout
        client.get('/logout')

        # تسجيل دخول مدير - Login as admin
        client.post('/login', data={
            'username': sample_admin_user.username,
            'password': 'testpassword'
        })

        # الوصول لصفحة المديرين - Access admin page
        response = client.get('/admin/settings')
        assert response.status_code == 200  # Success

    def test_api_token_validation(self, client):
        """اختبار التحقق من رمز API - Test API token validation"""
        # طلب بدون رمز - Request without token
        response = client.get('/api/v1/employees')
        assert response.status_code == 401  # Unauthorized

        # طلب برمز خاطئ - Request with invalid token
        headers = {'Authorization': 'Bearer invalid_token'}
        response = client.get('/api/v1/employees', headers=headers)
        assert response.status_code == 401  # Unauthorized

class TestDataSecurity:
    """اختبار أمان البيانات - Data security tests"""

    def test_sql_injection_prevention(self, client, auth_headers):
        """اختبار منع حقن SQL - Test SQL injection prevention"""
        # محاولة حقن SQL - SQL injection attempt
        malicious_input = "'; DROP TABLE users; --"

        response = client.get(f'/api/v1/employees?name={malicious_input}',
                            headers=auth_headers)

        # يجب أن يعود بخطأ أو نتيجة فارغة، وليس خطأ خادم - Should return error or empty result, not server error
        assert response.status_code != 500

    def test_xss_prevention(self, client, auth_headers):
        """اختبار منع XSS - Test XSS prevention"""
        # محاولة XSS - XSS attempt
        malicious_script = "<script>alert('XSS')</script>"

        response = client.post('/api/v1/employees',
                             json={
                                 'name': malicious_script,
                                 'job_title': 'Test',
                                 'branch_id': 1
                             },
                             headers=auth_headers)

        # التحقق من تنظيف البيانات - Verify data sanitization
        if response.status_code == 201:
            data = response.get_json()
            assert '<script>' not in data['data']['name']
```

---

## 🚀 تشغيل الاختبارات - Running Tests

### أوامر الاختبار الأساسية - Basic Test Commands

```bash
# تشغيل جميع الاختبارات - Run all tests
pytest

# تشغيل اختبارات محددة - Run specific tests
pytest tests/test_models.py
pytest tests/test_api.py::TestEmployeeAPI

# تشغيل مع تقرير التغطية - Run with coverage report
pytest --cov=app --cov-report=html

# تشغيل الاختبارات المتوازية - Run tests in parallel
pytest -n 4

# تشغيل اختبارات الأداء - Run performance tests
pytest tests/test_performance.py -v

# تشغيل اختبارات UI - Run UI tests
pytest tests/test_ui.py --driver Chrome

# تشغيل اختبار الحمولة - Run load testing
locust -f tests/performance/locustfile.py --host=http://localhost:5000
```

### إعداد التشغيل المستمر - Continuous Integration Setup

```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml

    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

---

## 📋 قائمة مراجعة الاختبار - Testing Checklist

### قبل الإطلاق - Pre-Release Checklist

- [ ] **اختبارات الوحدة** - Unit tests pass (90%+ coverage)
- [ ] **اختبارات التكامل** - Integration tests pass
- [ ] **اختبارات واجهة المستخدم** - UI tests pass
- [ ] **اختبارات الأداء** - Performance tests meet requirements
- [ ] **اختبارات الأمان** - Security tests pass
- [ ] **اختبار الكاميرا** - Camera functionality tested
- [ ] **اختبار التقارير** - Report generation tested
- [ ] **اختبار اللغات** - Multi-language support tested
- [ ] **اختبار المتصفحات** - Cross-browser compatibility tested
- [ ] **اختبار الأجهزة المحمولة** - Mobile device testing

### اختبارات الإنتاج - Production Testing

- [ ] **اختبار النشر** - Deployment testing
- [ ] **اختبار النسخ الاحتياطي** - Backup/restore testing
- [ ] **اختبار الأمان المتقدم** - Advanced security testing
- [ ] **اختبار الحمولة الكاملة** - Full load testing
- [ ] **اختبار التعافي من الكوارث** - Disaster recovery testing

---

*يجب تشغيل هذه الاختبارات بانتظام للحفاظ على جودة النظام وموثوقيته*

*These tests should be run regularly to maintain system quality and reliability*
```