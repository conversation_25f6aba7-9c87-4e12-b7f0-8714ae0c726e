# 🔒 دليل الأمان والحماية - Security Guide

## نظرة عامة - Overview

يحتوي هذا الدليل على إرشادات شاملة لحماية نظام الحضور والانصراف من التهديدات الأمنية وضمان خصوصية البيانات.

This guide contains comprehensive instructions for securing the attendance system against security threats and ensuring data privacy.

---

## 🛡️ أمان البيانات - Data Security

### حماية قاعدة البيانات - Database Security

#### تشفير البيانات - Data Encryption
```sql
-- تشفير كلمات المرور - Password Encryption
-- استخدام bcrypt لتشفير كلمات المرور
-- Using bcrypt for password hashing

-- مثال على إنشاء مستخدم آمن - Example of creating secure user
INSERT INTO users (username, password_hash) 
VALUES ('admin', '$2b$12$encrypted_password_hash');
```

#### النسخ الاحتياطي الآمن - Secure Backups
```bash
# تشفير النسخ الاحتياطية - Encrypt backups
gpg --symmetric --cipher-algo AES256 attendance_backup.db

# جدولة النسخ الاحتياطي المشفر - Schedule encrypted backups
0 2 * * * /opt/attendance-system/scripts/secure_backup.sh
```

### حماية الملفات - File Protection

#### صور الموظفين - Employee Photos
```bash
# تعيين صلاحيات آمنة للملفات - Set secure file permissions
chmod 640 /opt/attendance-system/uploads/employees/*
chown attendance:www-data /opt/attendance-system/uploads/employees/

# منع الوصول المباشر للصور - Prevent direct image access
# في ملف .htaccess أو nginx config
location /uploads/employees {
    deny all;
    return 403;
}
```

#### صور الكاميرا - Camera Images
```python
# تشفير صور الكاميرا - Encrypt camera images
from cryptography.fernet import Fernet

def encrypt_camera_image(image_path):
    key = Fernet.generate_key()
    fernet = Fernet(key)
    
    with open(image_path, 'rb') as file:
        original = file.read()
    
    encrypted = fernet.encrypt(original)
    
    with open(image_path + '.enc', 'wb') as encrypted_file:
        encrypted_file.write(encrypted)
```

---

## 🔐 المصادقة والتخويل - Authentication & Authorization

### إعدادات كلمات المرور - Password Settings

#### سياسة كلمات المرور القوية - Strong Password Policy
```python
# متطلبات كلمة المرور - Password requirements
PASSWORD_REQUIREMENTS = {
    'min_length': 8,
    'require_uppercase': True,
    'require_lowercase': True,
    'require_numbers': True,
    'require_special_chars': True,
    'max_age_days': 90,
    'history_count': 5  # منع إعادة استخدام آخر 5 كلمات مرور
}

def validate_password(password):
    """التحقق من قوة كلمة المرور - Validate password strength"""
    if len(password) < PASSWORD_REQUIREMENTS['min_length']:
        return False, "كلمة المرور قصيرة جداً - Password too short"
    
    if not re.search(r'[A-Z]', password):
        return False, "يجب أن تحتوي على حرف كبير - Must contain uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "يجب أن تحتوي على حرف صغير - Must contain lowercase letter"
    
    if not re.search(r'\d', password):
        return False, "يجب أن تحتوي على رقم - Must contain number"
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "يجب أن تحتوي على رمز خاص - Must contain special character"
    
    return True, "كلمة مرور قوية - Strong password"
```

### المصادقة الثنائية - Two-Factor Authentication

#### إعداد 2FA باستخدام TOTP
```python
import pyotp
import qrcode

def setup_2fa(user_id):
    """إعداد المصادقة الثنائية - Setup 2FA"""
    secret = pyotp.random_base32()
    
    # حفظ السر في قاعدة البيانات - Save secret in database
    user = User.query.get(user_id)
    user.totp_secret = secret
    db.session.commit()
    
    # إنشاء QR code للمستخدم - Generate QR code for user
    totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
        name=user.username,
        issuer_name="نظام الحضور - Attendance System"
    )
    
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(totp_uri)
    qr.make(fit=True)
    
    return qr.make_image(fill_color="black", back_color="white")

def verify_2fa(user_id, token):
    """التحقق من رمز 2FA - Verify 2FA token"""
    user = User.query.get(user_id)
    totp = pyotp.TOTP(user.totp_secret)
    return totp.verify(token, valid_window=1)
```

---

## 🌐 أمان الشبكة - Network Security

### إعدادات Firewall

#### UFW Configuration (Ubuntu)
```bash
# تفعيل الجدار الناري - Enable firewall
sudo ufw enable

# السماح بـ SSH - Allow SSH
sudo ufw allow ssh

# السماح بـ HTTP/HTTPS - Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# منع الوصول المباشر لقاعدة البيانات - Block direct database access
sudo ufw deny 5432/tcp

# عرض القواعد - Show rules
sudo ufw status verbose
```

#### Nginx Security Headers
```nginx
# إضافة headers الأمان - Add security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# إخفاء إصدار Nginx - Hide Nginx version
server_tokens off;

# منع الوصول للملفات الحساسة - Block access to sensitive files
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ \.(sql|conf|env)$ {
    deny all;
    access_log off;
    log_not_found off;
}
```

### حماية من DDoS

#### Rate Limiting
```nginx
# تحديد معدل الطلبات - Rate limiting
http {
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    server {
        # حماية صفحة تسجيل الدخول - Protect login page
        location /login {
            limit_req zone=login burst=3 nodelay;
            proxy_pass http://backend;
        }
        
        # حماية API - Protect API
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
        }
    }
}
```

---

## 📷 أمان الكاميرا - Camera Security

### حماية خصوصية الصور - Image Privacy Protection

#### تشويش الوجوه غير المصرح بها - Blur Unauthorized Faces
```python
import cv2
import face_recognition

def blur_unauthorized_faces(image_path, authorized_encodings):
    """تشويش الوجوه غير المصرح بها - Blur unauthorized faces"""
    image = cv2.imread(image_path)
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # العثور على الوجوه - Find faces
    face_locations = face_recognition.face_locations(rgb_image)
    face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
    
    for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
        # التحقق من التصريح - Check authorization
        matches = face_recognition.compare_faces(authorized_encodings, face_encoding)
        
        if not any(matches):
            # تشويش الوجه غير المصرح به - Blur unauthorized face
            face_region = image[top:bottom, left:right]
            blurred_face = cv2.GaussianBlur(face_region, (99, 99), 30)
            image[top:bottom, left:right] = blurred_face
    
    return image
```

#### حذف الصور التلقائي - Automatic Image Deletion
```python
import os
import datetime

def cleanup_old_camera_images():
    """حذف صور الكاميرا القديمة - Delete old camera images"""
    camera_dir = "/opt/attendance-system/uploads/camera_captures"
    retention_days = 30
    
    cutoff_date = datetime.datetime.now() - datetime.timedelta(days=retention_days)
    
    for filename in os.listdir(camera_dir):
        file_path = os.path.join(camera_dir, filename)
        file_time = datetime.datetime.fromtimestamp(os.path.getctime(file_path))
        
        if file_time < cutoff_date:
            os.remove(file_path)
            print(f"تم حذف الملف القديم - Deleted old file: {filename}")

# جدولة المهمة - Schedule task
# في crontab: 0 3 * * * python3 /opt/attendance-system/scripts/cleanup_images.py
```

---

## 🔍 مراقبة الأمان - Security Monitoring

### تسجيل الأحداث الأمنية - Security Event Logging

#### نظام تسجيل شامل - Comprehensive Logging System
```python
import logging
from datetime import datetime

# إعداد logger الأمان - Setup security logger
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('/opt/attendance-system/logs/security.log')
security_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s - IP: %(ip)s - User: %(user)s'
)
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

def log_security_event(event_type, message, ip_address, username=None):
    """تسجيل حدث أمني - Log security event"""
    security_logger.info(
        f"{event_type}: {message}",
        extra={'ip': ip_address, 'user': username or 'Anonymous'}
    )

# أمثلة على الاستخدام - Usage examples
log_security_event("LOGIN_SUCCESS", "تسجيل دخول ناجح", "*************", "admin")
log_security_event("LOGIN_FAILED", "محاولة دخول فاشلة", "*************", "admin")
log_security_event("CAMERA_ACCESS", "وصول للكاميرا", "*************", "employee1")
```

### كشف التسلل - Intrusion Detection

#### مراقبة المحاولات المشبوهة - Monitor Suspicious Attempts
```python
from collections import defaultdict
from datetime import datetime, timedelta

class SecurityMonitor:
    def __init__(self):
        self.failed_attempts = defaultdict(list)
        self.blocked_ips = set()
    
    def record_failed_login(self, ip_address, username):
        """تسجيل محاولة دخول فاشلة - Record failed login attempt"""
        now = datetime.now()
        self.failed_attempts[ip_address].append({
            'timestamp': now,
            'username': username
        })
        
        # تنظيف المحاولات القديمة - Clean old attempts
        cutoff = now - timedelta(minutes=15)
        self.failed_attempts[ip_address] = [
            attempt for attempt in self.failed_attempts[ip_address]
            if attempt['timestamp'] > cutoff
        ]
        
        # حظر IP بعد 5 محاولات فاشلة - Block IP after 5 failed attempts
        if len(self.failed_attempts[ip_address]) >= 5:
            self.block_ip(ip_address)
            log_security_event(
                "IP_BLOCKED", 
                f"تم حظر IP بسبب محاولات دخول متكررة - IP blocked for repeated failed attempts",
                ip_address
            )
    
    def block_ip(self, ip_address):
        """حظر عنوان IP - Block IP address"""
        self.blocked_ips.add(ip_address)
        # إضافة قاعدة firewall - Add firewall rule
        os.system(f"sudo ufw deny from {ip_address}")
    
    def is_blocked(self, ip_address):
        """التحقق من حظر IP - Check if IP is blocked"""
        return ip_address in self.blocked_ips
```

---

## 📋 تدقيق الأمان - Security Auditing

### فحص دوري للأمان - Regular Security Checks

#### سكريبت فحص الأمان - Security Check Script
```bash
#!/bin/bash
# security_audit.sh

echo "=== فحص أمان نظام الحضور - Attendance System Security Audit ==="
echo "التاريخ - Date: $(date)"
echo

# فحص صلاحيات الملفات - Check file permissions
echo "1. فحص صلاحيات الملفات - File Permissions Check:"
find /opt/attendance-system -type f -perm /o+w -exec ls -l {} \;

# فحص المستخدمين المشبوهين - Check suspicious users
echo "2. فحص المستخدمين - User Check:"
awk -F: '$3 >= 1000 {print $1}' /etc/passwd

# فحص الاتصالات النشطة - Check active connections
echo "3. الاتصالات النشطة - Active Connections:"
netstat -tulpn | grep :5000

# فحص محاولات الدخول الفاشلة - Check failed login attempts
echo "4. محاولات الدخول الفاشلة - Failed Login Attempts:"
grep "LOGIN_FAILED" /opt/attendance-system/logs/security.log | tail -10

# فحص مساحة القرص - Check disk space
echo "5. مساحة القرص - Disk Space:"
df -h /opt/attendance-system

# فحص العمليات المشبوهة - Check suspicious processes
echo "6. العمليات النشطة - Active Processes:"
ps aux | grep -E "(python|gunicorn)" | grep attendance

echo "=== انتهى الفحص - Audit Complete ==="
```

### تقرير الأمان الشهري - Monthly Security Report

#### إنشاء تقرير شامل - Generate Comprehensive Report
```python
def generate_security_report(month, year):
    """إنشاء تقرير أمان شهري - Generate monthly security report"""
    report = {
        'period': f"{month}/{year}",
        'login_attempts': {
            'successful': 0,
            'failed': 0,
            'blocked_ips': []
        },
        'camera_usage': {
            'total_recognitions': 0,
            'failed_recognitions': 0,
            'unauthorized_attempts': 0
        },
        'system_health': {
            'uptime': 0,
            'errors': 0,
            'warnings': 0
        }
    }
    
    # تحليل سجلات الأمان - Analyze security logs
    with open('/opt/attendance-system/logs/security.log', 'r') as f:
        for line in f:
            if f"{month}/{year}" in line:
                if "LOGIN_SUCCESS" in line:
                    report['login_attempts']['successful'] += 1
                elif "LOGIN_FAILED" in line:
                    report['login_attempts']['failed'] += 1
                elif "CAMERA_RECOGNITION_SUCCESS" in line:
                    report['camera_usage']['total_recognitions'] += 1
                elif "CAMERA_RECOGNITION_FAILED" in line:
                    report['camera_usage']['failed_recognitions'] += 1
    
    return report
```

---

## 🚨 الاستجابة للحوادث - Incident Response

### خطة الاستجابة للطوارئ - Emergency Response Plan

#### خطوات الاستجابة السريعة - Quick Response Steps
```bash
# 1. إيقاف النظام فوراً - Immediate system shutdown
sudo supervisorctl stop attendance-system
sudo systemctl stop nginx

# 2. عزل الخادم - Isolate server
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default deny outgoing
sudo ufw allow out 22/tcp  # SSH only

# 3. إنشاء نسخة احتياطية طارئة - Create emergency backup
sudo cp -r /opt/attendance-system /opt/emergency-backup-$(date +%Y%m%d_%H%M%S)

# 4. فحص السجلات - Check logs
tail -100 /opt/attendance-system/logs/security.log
tail -100 /var/log/nginx/error.log
tail -100 /var/log/auth.log

# 5. إشعار الفريق - Notify team
echo "حادث أمني في نظام الحضور - Security incident in attendance system" | \
mail -s "URGENT: Security Alert" <EMAIL>
```

### استعادة النظام - System Recovery

#### خطوات الاستعادة الآمنة - Safe Recovery Steps
```bash
# 1. فحص النظام - System scan
sudo rkhunter --check
sudo chkrootkit

# 2. تحديث النظام - Update system
sudo apt update && sudo apt upgrade -y

# 3. تغيير كلمات المرور - Change passwords
sudo passwd attendance
# تغيير كلمات مرور قاعدة البيانات - Change database passwords

# 4. إعادة تشغيل الخدمات - Restart services
sudo supervisorctl start attendance-system
sudo systemctl start nginx

# 5. مراقبة مكثفة - Intensive monitoring
tail -f /opt/attendance-system/logs/security.log
```

---

## 📚 الامتثال والمعايير - Compliance & Standards

### GDPR Compliance

#### حماية البيانات الشخصية - Personal Data Protection
```python
class GDPRCompliance:
    def __init__(self):
        self.data_retention_days = 365 * 2  # سنتان - 2 years
    
    def anonymize_employee_data(self, employee_id):
        """إخفاء هوية بيانات الموظف - Anonymize employee data"""
        employee = Employee.query.get(employee_id)
        employee.name = f"Employee_{employee_id}"
        employee.email = f"anonymized_{employee_id}@company.com"
        employee.phone = "***-***-****"
        db.session.commit()
    
    def delete_old_attendance_data(self):
        """حذف بيانات الحضور القديمة - Delete old attendance data"""
        cutoff_date = datetime.now() - timedelta(days=self.data_retention_days)
        old_records = Attendance.query.filter(Attendance.date < cutoff_date).all()
        
        for record in old_records:
            db.session.delete(record)
        
        db.session.commit()
    
    def export_employee_data(self, employee_id):
        """تصدير بيانات الموظف - Export employee data"""
        employee = Employee.query.get(employee_id)
        attendance_records = Attendance.query.filter_by(emp_id=employee_id).all()
        
        data = {
            'employee_info': {
                'name': employee.name,
                'email': employee.email,
                'phone': employee.phone,
                'hire_date': employee.hire_date.isoformat()
            },
            'attendance_records': [
                {
                    'date': record.date.isoformat(),
                    'time_in': record.time_in.isoformat() if record.time_in else None,
                    'time_out': record.time_out.isoformat() if record.time_out else None
                }
                for record in attendance_records
            ]
        }
        
        return data
```

---

## 🔧 أدوات الأمان - Security Tools

### أدوات المراقبة الموصى بها - Recommended Monitoring Tools

```bash
# تثبيت أدوات الأمان - Install security tools
sudo apt install -y fail2ban rkhunter chkrootkit logwatch

# إعداد fail2ban - Configure fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo nano /etc/fail2ban/jail.local

# إعداد مراقبة تطبيق Flask - Flask application monitoring
[flask-attendance]
enabled = true
port = 5000
filter = flask-attendance
logpath = /opt/attendance-system/logs/security.log
maxretry = 5
bantime = 3600
```

### فحص الثغرات الأمنية - Vulnerability Scanning

```bash
# فحص المنافذ - Port scanning
nmap -sS -O localhost

# فحص تطبيق الويب - Web application scanning
nikto -h http://localhost:5000

# فحص SSL - SSL testing
testssl.sh https://your-domain.com
```

---

## 📞 جهات الاتصال الطارئة - Emergency Contacts

### فريق الأمان - Security Team
- **مدير الأمان - Security Manager**: [رقم الهاتف - Phone Number]
- **مطور النظام - System Developer**: [رقم الهاتف - Phone Number]
- **مدير الشبكة - Network Administrator**: [رقم الهاتف - Phone Number]

### إجراءات الإبلاغ - Reporting Procedures
1. **الحوادث الطارئة - Emergency Incidents**: اتصال فوري - Immediate call
2. **الثغرات الأمنية - Security Vulnerabilities**: بريد إلكتروني خلال 24 ساعة - Email within 24 hours
3. **الصيانة الدورية - Routine Maintenance**: تقرير أسبوعي - Weekly report

---

*يجب مراجعة هذا الدليل وتحديثه بانتظام للحفاظ على أعلى مستويات الأمان*

*This guide should be reviewed and updated regularly to maintain the highest security standards*
