#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحضور والانصراف - تشغيل سريع
Attendance System - Quick Run

تشغيل فوري للنظام على المنفذ 9999
Instant system startup on port 9999
"""

import os
import sys
from pathlib import Path

def print_welcome():
    """طباعة رسالة الترحيب - Print welcome message"""
    print("🚀 نظام الحضور والانصراف - Attendance System")
    print("=" * 60)
    print("🌐 المنفذ الجديد - New Port: 9999")
    print("🔗 الرابط - URL: http://localhost:9999")
    print("👤 بيانات الدخول - Login: admin / admin123")
    print("=" * 60)

def setup_environment():
    """إعداد البيئة الأساسية - Setup basic environment"""
    # إنشاء المجلدات الأساسية
    directories = [
        'static/uploads/employees',
        'static/uploads/camera_captures',
        'static/uploads/reports',
        'logs',
        'data'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if not os.path.exists('.env'):
        env_content = """# نظام الحضور والانصراف - إعدادات البيئة
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=9999
SECRET_KEY=dev-secret-key-change-in-production
DATABASE_URL=sqlite:///data/attendance.db
DEFAULT_LANGUAGE=ar
CAMERA_ENABLED=True
"""
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)

def check_basic_requirements():
    """التحقق من المتطلبات الأساسية - Check basic requirements"""
    missing_packages = []

    try:
        import flask
    except ImportError:
        missing_packages.append('flask')

    try:
        import flask_sqlalchemy
    except ImportError:
        missing_packages.append('flask-sqlalchemy')

    try:
        import flask_login
    except ImportError:
        missing_packages.append('flask-login')

    if missing_packages:
        print("❌ المكتبات التالية مفقودة - Missing packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 لتثبيت المتطلبات الأساسية:")
        print("pip install -r requirements-basic.txt")
        print("\n💡 أو لتثبيت المتطلبات الكاملة:")
        print("pip install -r requirements.txt")
        return False

    return True

def main():
    """الوظيفة الرئيسية - Main function"""
    print_welcome()

    # التحقق من المتطلبات الأساسية
    if not check_basic_requirements():
        return

    # إعداد البيئة
    setup_environment()

    # تشغيل النظام
    try:
        print("⚡ بدء تشغيل النظام...")
        print("⌨️  اضغط Ctrl+C للإيقاف")
        print()

        # استيراد وتشغيل التطبيق
        from app import app
        app.run(
            host='0.0.0.0',
            port=9999,
            debug=True,
            use_reloader=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("💡 تأكد من وجود ملف app.py")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ المنفذ 9999 مستخدم بالفعل")
            print("💡 أوقف التطبيق الآخر أو استخدم منفذ مختلف")
        else:
            print(f"❌ خطأ في النظام: {e}")
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام")
        print("👋 شكراً لاستخدام نظام الحضور والانصراف!")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == '__main__':
    main()
