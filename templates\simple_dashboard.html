{% extends "simple_base.html" %}

{% block title %}لوحة التحكم - نظام الحضور والانصراف{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="bi bi-speedometer2 me-2"></i>
                    لوحة التحكم
                </h2>
                <p class="card-text text-muted">
                    مرحباً {{ current_user.username }}، إليك نظرة عامة على النظام
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-people-fill" style="font-size: 3rem; color: var(--primary-color);"></i>
                <h3 class="mt-3">{{ total_employees }}</h3>
                <p class="text-muted">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-building" style="font-size: 3rem; color: var(--secondary-color);"></i>
                <h3 class="mt-3">{{ total_branches }}</h3>
                <p class="text-muted">الفروع</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-calendar-check" style="font-size: 3rem; color: var(--success-color);"></i>
                <h3 class="mt-3">{{ today_attendance }}</h3>
                <p class="text-muted">حضور اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-clock" style="font-size: 3rem; color: var(--warning-color);"></i>
                <h3 class="mt-3" id="current-time">--:--</h3>
                <p class="text-muted">الوقت الحالي</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_employee') }}" class="btn btn-success w-100">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة موظف
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('manual_attendance') }}" class="btn btn-primary w-100">
                            <i class="bi bi-clock me-2"></i>
                            تسجيل حضور
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees_list') }}" class="btn btn-info w-100">
                            <i class="bi bi-people me-2"></i>
                            عرض الموظفين
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('reports') }}" class="btn btn-warning w-100">
                            <i class="bi bi-file-earmark-bar-graph me-2"></i>
                            مركز التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Present Today -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-check me-2"></i>
                    الموظفون الحاضرون اليوم
                </h5>
            </div>
            <div class="card-body">
                {% if present_today %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الموظف</th>
                                <th>الاسم</th>
                                <th>المنصب</th>
                                <th>وقت الدخول</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in present_today %}
                            <tr>
                                <td>{{ employee.employee_code }}</td>
                                <td>{{ employee.name_ar or employee.name }}</td>
                                <td>{{ employee.job_title or 'غير محدد' }}</td>
                                <td>
                                    {% for record in employee.attendance_records %}
                                        {% if record.date == today and record.time_in %}
                                            {{ record.time_in.strftime('%H:%M') }}
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>
                                    <span class="badge bg-success">حاضر</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-calendar-x" style="font-size: 3rem; color: #dee2e6;"></i>
                    <p class="text-muted mt-3">لا يوجد موظفون حاضرون اليوم</p>
                    <a href="{{ url_for('manual_attendance') }}" class="btn btn-primary">
                        <i class="bi bi-clock me-2"></i>
                        تسجيل حضور
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الوقت الحالي
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        document.getElementById('current-time').textContent = timeString;
    }
    
    // تحديث الوقت عند تحميل الصفحة
    updateTime();
    
    // تحديث الوقت كل ثانية
    setInterval(updateTime, 1000);
    
    // تحديث الصفحة كل 5 دقائق
    setTimeout(() => {
        location.reload();
    }, 5 * 60 * 1000);
</script>
{% endblock %}
