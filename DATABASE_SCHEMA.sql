-- ===================================================================
-- نظام الحضور والانصراف - قاعدة البيانات
-- Attendance and Departure System - Database Schema
-- ===================================================================

-- إعدادات قاعدة البيانات - Database Settings
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 1000;
PRAGMA temp_store = memory;

-- ===================================================================
-- جدول المستخدمين - Users Table
-- ===================================================================
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'hr', 'manager', 'employee')),
    is_active BOOLEAN DEFAULT 1,
    last_login TIMESTAMP,
    totp_secret VARCHAR(32), -- للمصادقة الثنائية - For 2FA
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للبحث السريع - Index for quick search
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- ===================================================================
-- جدول الفروع - Branches Table
-- ===================================================================
CREATE TABLE branches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100), -- الاسم بالعربية - Arabic name
    address TEXT,
    address_ar TEXT, -- العنوان بالعربية - Arabic address
    phone VARCHAR(20),
    email VARCHAR(100),
    manager_name VARCHAR(100),
    manager_name_ar VARCHAR(100), -- اسم المدير بالعربية - Manager name in Arabic
    timezone VARCHAR(50) DEFAULT 'UTC',
    working_hours_start TIME DEFAULT '08:00:00',
    working_hours_end TIME DEFAULT '17:00:00',
    late_threshold_minutes INTEGER DEFAULT 15,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس للفروع - Branch indexes
CREATE INDEX idx_branches_name ON branches(name);
CREATE INDEX idx_branches_active ON branches(is_active);

-- ===================================================================
-- جدول الموظفين - Employees Table
-- ===================================================================
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_code VARCHAR(20) UNIQUE NOT NULL, -- رقم الموظف - Employee number
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100), -- الاسم بالعربية - Arabic name
    job_title VARCHAR(100),
    job_title_ar VARCHAR(100), -- المسمى الوظيفي بالعربية - Job title in Arabic
    department VARCHAR(100),
    department_ar VARCHAR(100), -- القسم بالعربية - Department in Arabic
    branch_id INTEGER NOT NULL,
    direct_manager_id INTEGER, -- المدير المباشر - Direct manager
    phone VARCHAR(20),
    email VARCHAR(100),
    national_id VARCHAR(20) UNIQUE, -- رقم الهوية - National ID
    hire_date DATE NOT NULL,
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'ذكر', 'أنثى')),
    nationality VARCHAR(50),
    salary DECIMAL(10,2),
    image_path VARCHAR(255), -- مسار صورة الموظف - Employee photo path
    face_encoding TEXT, -- ترميز الوجه للتعرف - Face encoding for recognition
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave')),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    address TEXT,
    address_ar TEXT, -- العنوان بالعربية - Arabic address
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches (id),
    FOREIGN KEY (direct_manager_id) REFERENCES employees (id)
);

-- فهارس الموظفين - Employee indexes
CREATE INDEX idx_employees_code ON employees(employee_code);
CREATE INDEX idx_employees_name ON employees(name);
CREATE INDEX idx_employees_branch ON employees(branch_id);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_employees_manager ON employees(direct_manager_id);

-- ===================================================================
-- جدول الحضور والانصراف - Attendance Table
-- ===================================================================
CREATE TABLE attendance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    emp_id INTEGER NOT NULL,
    date DATE NOT NULL,
    time_in TIME,
    time_out TIME,
    break_time_out TIME, -- وقت خروج للاستراحة - Break out time
    break_time_in TIME, -- وقت عودة من الاستراحة - Break in time
    total_break_minutes INTEGER DEFAULT 0,
    total_work_minutes INTEGER, -- إجمالي دقائق العمل - Total work minutes
    overtime_minutes INTEGER DEFAULT 0, -- دقائق العمل الإضافي - Overtime minutes
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'early_leave', 'half_day', 'sick_leave', 'vacation')),
    attendance_method VARCHAR(20) DEFAULT 'manual' CHECK (attendance_method IN ('manual', 'camera', 'fingerprint', 'card', 'mobile')),
    camera_img_in VARCHAR(255), -- صورة دخول الكاميرا - Camera in image
    camera_img_out VARCHAR(255), -- صورة خروج الكاميرا - Camera out image
    recognition_confidence DECIMAL(3,2), -- ثقة التعرف على الوجه - Face recognition confidence
    ip_address VARCHAR(45), -- عنوان IP - IP address
    device_info TEXT, -- معلومات الجهاز - Device information
    location_lat DECIMAL(10,8), -- خط العرض - Latitude
    location_lng DECIMAL(11,8), -- خط الطول - Longitude
    notes TEXT,
    approved_by INTEGER, -- معتمد من قبل - Approved by
    approval_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emp_id) REFERENCES employees (id),
    FOREIGN KEY (approved_by) REFERENCES users (id)
);

-- فهارس الحضور - Attendance indexes
CREATE INDEX idx_attendance_emp_date ON attendance(emp_id, date);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_status ON attendance(status);
CREATE INDEX idx_attendance_method ON attendance(attendance_method);

-- ===================================================================
-- جدول طلبات تصحيح الحضور - Attendance Correction Requests
-- ===================================================================
CREATE TABLE attendance_corrections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    emp_id INTEGER NOT NULL,
    attendance_id INTEGER, -- معرف سجل الحضور المراد تصحيحه - Attendance record to correct
    request_date DATE NOT NULL,
    original_time_in TIME,
    original_time_out TIME,
    requested_time_in TIME,
    requested_time_out TIME,
    reason TEXT NOT NULL,
    reason_ar TEXT, -- السبب بالعربية - Reason in Arabic
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    requested_by INTEGER NOT NULL,
    reviewed_by INTEGER,
    review_date TIMESTAMP,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emp_id) REFERENCES employees (id),
    FOREIGN KEY (attendance_id) REFERENCES attendance (id),
    FOREIGN KEY (requested_by) REFERENCES users (id),
    FOREIGN KEY (reviewed_by) REFERENCES users (id)
);

-- فهارس طلبات التصحيح - Correction request indexes
CREATE INDEX idx_corrections_emp ON attendance_corrections(emp_id);
CREATE INDEX idx_corrections_status ON attendance_corrections(status);
CREATE INDEX idx_corrections_date ON attendance_corrections(request_date);

-- ===================================================================
-- جدول الإجازات - Leaves Table
-- ===================================================================
CREATE TABLE leaves (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    emp_id INTEGER NOT NULL,
    leave_type VARCHAR(50) NOT NULL CHECK (leave_type IN ('annual', 'sick', 'emergency', 'maternity', 'paternity', 'unpaid')),
    leave_type_ar VARCHAR(50), -- نوع الإجازة بالعربية - Leave type in Arabic
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_days INTEGER NOT NULL,
    reason TEXT,
    reason_ar TEXT, -- السبب بالعربية - Reason in Arabic
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    requested_by INTEGER NOT NULL,
    approved_by INTEGER,
    approval_date TIMESTAMP,
    approval_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emp_id) REFERENCES employees (id),
    FOREIGN KEY (requested_by) REFERENCES users (id),
    FOREIGN KEY (approved_by) REFERENCES users (id)
);

-- فهارس الإجازات - Leave indexes
CREATE INDEX idx_leaves_emp ON leaves(emp_id);
CREATE INDEX idx_leaves_dates ON leaves(start_date, end_date);
CREATE INDEX idx_leaves_status ON leaves(status);

-- ===================================================================
-- جدول الأعياد والعطل الرسمية - Holidays Table
-- ===================================================================
CREATE TABLE holidays (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100), -- اسم العطلة بالعربية - Holiday name in Arabic
    date DATE NOT NULL,
    is_recurring BOOLEAN DEFAULT 0, -- عطلة متكررة سنوياً - Recurring annually
    holiday_type VARCHAR(50) DEFAULT 'national' CHECK (holiday_type IN ('national', 'religious', 'company')),
    description TEXT,
    description_ar TEXT, -- الوصف بالعربية - Description in Arabic
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس الأعياد - Holiday index
CREATE INDEX idx_holidays_date ON holidays(date);
CREATE INDEX idx_holidays_active ON holidays(is_active);

-- ===================================================================
-- جدول إعدادات النظام - System Settings Table
-- ===================================================================
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'boolean', 'json')),
    description TEXT,
    description_ar TEXT, -- الوصف بالعربية - Description in Arabic
    is_editable BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس الإعدادات - Settings index
CREATE INDEX idx_settings_key ON system_settings(setting_key);

-- ===================================================================
-- جدول سجل النشاطات - Activity Log Table
-- ===================================================================
CREATE TABLE activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    emp_id INTEGER,
    action VARCHAR(100) NOT NULL,
    action_ar VARCHAR(100), -- الإجراء بالعربية - Action in Arabic
    table_name VARCHAR(50),
    record_id INTEGER,
    old_values TEXT, -- القيم القديمة (JSON) - Old values (JSON)
    new_values TEXT, -- القيم الجديدة (JSON) - New values (JSON)
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (emp_id) REFERENCES employees (id)
);

-- فهارس سجل النشاطات - Activity log indexes
CREATE INDEX idx_activity_user ON activity_log(user_id);
CREATE INDEX idx_activity_emp ON activity_log(emp_id);
CREATE INDEX idx_activity_action ON activity_log(action);
CREATE INDEX idx_activity_date ON activity_log(created_at);

-- ===================================================================
-- جدول التقارير المحفوظة - Saved Reports Table
-- ===================================================================
CREATE TABLE saved_reports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    report_name VARCHAR(100) NOT NULL,
    report_name_ar VARCHAR(100), -- اسم التقرير بالعربية - Report name in Arabic
    report_type VARCHAR(50) NOT NULL,
    parameters TEXT, -- معاملات التقرير (JSON) - Report parameters (JSON)
    file_path VARCHAR(255),
    file_size INTEGER,
    generated_by INTEGER NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    download_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT 0,
    FOREIGN KEY (generated_by) REFERENCES users (id)
);

-- فهرس التقارير - Reports index
CREATE INDEX idx_reports_type ON saved_reports(report_type);
CREATE INDEX idx_reports_user ON saved_reports(generated_by);
CREATE INDEX idx_reports_date ON saved_reports(generated_at);

-- ===================================================================
-- إدراج البيانات الأساسية - Insert Initial Data
-- ===================================================================

-- إدراج المستخدم الافتراضي - Insert default admin user
INSERT INTO users (username, password_hash, email, role) VALUES 
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlG.', '<EMAIL>', 'admin');

-- إدراج الفرع الرئيسي - Insert main branch
INSERT INTO branches (name, name_ar, address, address_ar, phone, email, manager_name, manager_name_ar) VALUES 
('Main Branch', 'الفرع الرئيسي', '123 Main Street', 'شارع الرئيسي 123', '+966112345678', '<EMAIL>', 'John Manager', 'أحمد المدير');

-- إدراج إعدادات النظام الافتراضية - Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, description_ar) VALUES 
('default_language', 'ar', 'string', 'Default system language', 'اللغة الافتراضية للنظام'),
('working_hours_start', '08:00', 'string', 'Default working hours start time', 'وقت بداية العمل الافتراضي'),
('working_hours_end', '17:00', 'string', 'Default working hours end time', 'وقت نهاية العمل الافتراضي'),
('late_threshold_minutes', '15', 'integer', 'Minutes after which arrival is considered late', 'الدقائق التي بعدها يعتبر الوصول متأخراً'),
('camera_recognition_threshold', '0.6', 'string', 'Face recognition confidence threshold', 'عتبة الثقة في التعرف على الوجه'),
('max_file_size_mb', '10', 'integer', 'Maximum file upload size in MB', 'الحد الأقصى لحجم الملف المرفوع بالميجابايت'),
('backup_retention_days', '30', 'integer', 'Number of days to keep backups', 'عدد الأيام للاحتفاظ بالنسخ الاحتياطية'),
('session_timeout_minutes', '60', 'integer', 'User session timeout in minutes', 'انتهاء صلاحية جلسة المستخدم بالدقائق');

-- إدراج بعض الأعياد الافتراضية - Insert some default holidays
INSERT INTO holidays (name, name_ar, date, is_recurring, holiday_type, description, description_ar) VALUES 
('New Year', 'رأس السنة الميلادية', '2024-01-01', 1, 'national', 'New Year Day', 'يوم رأس السنة الميلادية'),
('National Day', 'اليوم الوطني', '2024-09-23', 1, 'national', 'Saudi National Day', 'اليوم الوطني السعودي'),
('Eid Al-Fitr', 'عيد الفطر', '2024-04-10', 0, 'religious', 'End of Ramadan celebration', 'عيد الفطر المبارك'),
('Eid Al-Adha', 'عيد الأضحى', '2024-06-16', 0, 'religious', 'Festival of Sacrifice', 'عيد الأضحى المبارك');

-- ===================================================================
-- إنشاء Views للاستعلامات المتكررة - Create Views for Common Queries
-- ===================================================================

-- عرض الحضور اليومي - Daily Attendance View
CREATE VIEW daily_attendance_summary AS
SELECT 
    a.date,
    b.name as branch_name,
    b.name_ar as branch_name_ar,
    COUNT(a.id) as total_records,
    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
    COUNT(CASE WHEN a.attendance_method = 'camera' THEN 1 END) as camera_count,
    COUNT(CASE WHEN a.attendance_method = 'manual' THEN 1 END) as manual_count
FROM attendance a
JOIN employees e ON a.emp_id = e.id
JOIN branches b ON e.branch_id = b.id
GROUP BY a.date, b.id, b.name, b.name_ar;

-- عرض إحصائيات الموظفين - Employee Statistics View
CREATE VIEW employee_statistics AS
SELECT 
    e.id,
    e.employee_code,
    e.name,
    e.name_ar,
    b.name as branch_name,
    b.name_ar as branch_name_ar,
    COUNT(a.id) as total_attendance_days,
    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
    AVG(a.total_work_minutes) as avg_work_minutes,
    SUM(a.overtime_minutes) as total_overtime_minutes
FROM employees e
LEFT JOIN attendance a ON e.id = a.emp_id
JOIN branches b ON e.branch_id = b.id
WHERE e.status = 'active'
GROUP BY e.id, e.employee_code, e.name, e.name_ar, b.name, b.name_ar;

-- ===================================================================
-- إنشاء Triggers للتحديث التلقائي - Create Triggers for Auto Updates
-- ===================================================================

-- تحديث وقت التعديل تلقائياً - Auto update modified time
CREATE TRIGGER update_users_timestamp 
    AFTER UPDATE ON users
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_employees_timestamp 
    AFTER UPDATE ON employees
BEGIN
    UPDATE employees SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_attendance_timestamp 
    AFTER UPDATE ON attendance
BEGIN
    UPDATE attendance SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- حساب إجمالي ساعات العمل تلقائياً - Auto calculate total work hours
CREATE TRIGGER calculate_work_hours 
    AFTER UPDATE OF time_out ON attendance
    WHEN NEW.time_out IS NOT NULL AND NEW.time_in IS NOT NULL
BEGIN
    UPDATE attendance 
    SET total_work_minutes = (
        (strftime('%s', NEW.time_out) - strftime('%s', NEW.time_in)) / 60
    ) - COALESCE(NEW.total_break_minutes, 0)
    WHERE id = NEW.id;
END;

-- ===================================================================
-- إنشاء فهارس إضافية للأداء - Additional Performance Indexes
-- ===================================================================

-- فهارس مركبة للاستعلامات المعقدة - Composite indexes for complex queries
CREATE INDEX idx_attendance_emp_date_status ON attendance(emp_id, date, status);
CREATE INDEX idx_employees_branch_status ON employees(branch_id, status);
CREATE INDEX idx_activity_log_date_user ON activity_log(created_at, user_id);

-- ===================================================================
-- نهاية ملف قاعدة البيانات - End of Database Schema
-- ===================================================================

-- تحسين قاعدة البيانات - Optimize database
ANALYZE;
VACUUM;
